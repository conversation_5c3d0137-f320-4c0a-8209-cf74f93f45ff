<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Bill;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Payment::with(['bill.tenant', 'bill.unit', 'bill.property', 'tenant', 'processedBy']);
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Payment::whereHas('bill', function ($q) use ($propertyIds) {
                $q->whereIn('property_id', $propertyIds);
            })->with(['bill.tenant', 'bill.unit', 'bill.property', 'tenant', 'processedBy']);
        } else {
            // Tenants see only their payments
            $query = Payment::where('tenant_id', $user->id)->with(['bill.unit', 'bill.property', 'processedBy']);
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('payment_number', 'like', "%{$search}%")
                  ->orWhere('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('tenant', function ($tq) use ($search) {
                      $tq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->latest()->paginate(15);

        // Calculate totals
        $totals = [
            'total_amount' => $query->sum('amount'),
            'completed_amount' => $query->where('status', 'completed')->sum('amount'),
            'pending_amount' => $query->where('status', 'pending')->sum('amount'),
            'failed_amount' => $query->where('status', 'failed')->sum('amount'),
        ];

        return view('payments.index', compact('payments', 'totals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Payment::class);
        
        $user = Auth::user();
        
        // Get unpaid bills based on user role
        if ($user->hasRole('admin')) {
            $bills = Bill::where('status', 'unpaid')->with(['tenant', 'unit', 'property'])->get();
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $bills = Bill::whereIn('property_id', $propertyIds)->where('status', 'unpaid')->with(['tenant', 'unit', 'property'])->get();
        } else {
            $bills = Bill::where('tenant_id', $user->id)->where('status', 'unpaid')->with(['unit', 'property'])->get();
        }

        $selectedBill = $request->bill_id ? Bill::find($request->bill_id) : null;

        return view('payments.create', compact('bills', 'selectedBill'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'bill_id' => 'required|exists:bills,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,check,bank_transfer,credit_card,online',
            'payment_date' => 'required|date',
            'transaction_id' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'receipt_number' => 'nullable|string|max:255',
        ]);

        $bill = Bill::findOrFail($validated['bill_id']);

        // Check if payment amount doesn't exceed remaining bill amount
        $paidAmount = $bill->payments()->where('status', 'completed')->sum('amount');
        $remainingAmount = $bill->total_amount - $paidAmount;

        if ($validated['amount'] > $remainingAmount) {
            return back()->withErrors(['amount' => 'Payment amount cannot exceed remaining bill amount of $' . number_format($remainingAmount, 2)])->withInput();
        }

        // Generate unique payment number
        $validated['payment_number'] = 'PAY-' . date('Y') . '-' . str_pad(Payment::count() + 1, 6, '0', STR_PAD_LEFT);
        $validated['tenant_id'] = $bill->tenant_id;
        $validated['processed_by'] = Auth::id();
        $validated['status'] = 'completed'; // Default to completed for manual payments

        $payment = Payment::create($validated);

        // Update bill status if fully paid
        $totalPaid = $bill->payments()->where('status', 'completed')->sum('amount');
        if ($totalPaid >= $bill->total_amount) {
            $bill->update(['status' => 'paid']);
        }

        return redirect()->route('payments.show', $payment)
            ->with('success', 'Payment recorded successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->load(['bill.tenant', 'bill.unit', 'bill.property', 'tenant', 'processedBy']);

        return view('payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        $this->authorize('update', $payment);
        
        $user = Auth::user();
        
        // Get bills based on user role
        if ($user->hasRole('admin')) {
            $bills = Bill::with(['tenant', 'unit', 'property'])->get();
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $bills = Bill::whereIn('property_id', $propertyIds)->with(['tenant', 'unit', 'property'])->get();
        } else {
            $bills = Bill::where('tenant_id', $user->id)->with(['unit', 'property'])->get();
        }

        return view('payments.edit', compact('payment', 'bills'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        $validated = $request->validate([
            'bill_id' => 'required|exists:bills,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,check,bank_transfer,credit_card,online',
            'payment_date' => 'required|date',
            'transaction_id' => 'nullable|string|max:255',
            'status' => 'required|in:pending,completed,failed,cancelled',
            'notes' => 'nullable|string',
            'receipt_number' => 'nullable|string|max:255',
        ]);

        $bill = Bill::findOrFail($validated['bill_id']);

        // Check if payment amount doesn't exceed remaining bill amount (excluding current payment)
        $paidAmount = $bill->payments()->where('status', 'completed')->where('id', '!=', $payment->id)->sum('amount');
        $remainingAmount = $bill->total_amount - $paidAmount;

        if ($validated['amount'] > $remainingAmount) {
            return back()->withErrors(['amount' => 'Payment amount cannot exceed remaining bill amount of $' . number_format($remainingAmount, 2)])->withInput();
        }

        $payment->update($validated);

        // Update bill status
        $totalPaid = $bill->payments()->where('status', 'completed')->sum('amount');
        if ($totalPaid >= $bill->total_amount) {
            $bill->update(['status' => 'paid']);
        } else {
            $bill->update(['status' => 'unpaid']);
        }

        return redirect()->route('payments.show', $payment)
            ->with('success', 'Payment updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        $this->authorize('delete', $payment);

        $bill = $payment->bill;
        $payment->delete();

        // Update bill status
        $totalPaid = $bill->payments()->where('status', 'completed')->sum('amount');
        if ($totalPaid >= $bill->total_amount) {
            $bill->update(['status' => 'paid']);
        } else {
            $bill->update(['status' => 'unpaid']);
        }

        return redirect()->route('payments.index')
            ->with('success', 'Payment deleted successfully!');
    }

    /**
     * Process online payment
     */
    public function processOnlinePayment(Request $request, Bill $bill)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:credit_card,online',
        ]);

        // Check if payment amount doesn't exceed remaining bill amount
        $paidAmount = $bill->payments()->where('status', 'completed')->sum('amount');
        $remainingAmount = $bill->total_amount - $paidAmount;

        if ($validated['amount'] > $remainingAmount) {
            return back()->withErrors(['amount' => 'Payment amount cannot exceed remaining bill amount of $' . number_format($remainingAmount, 2)])->withInput();
        }

        // Here you would integrate with payment gateway
        // For demo purposes, we'll simulate a successful payment

        $payment = Payment::create([
            'payment_number' => 'PAY-' . date('Y') . '-' . str_pad(Payment::count() + 1, 6, '0', STR_PAD_LEFT),
            'bill_id' => $bill->id,
            'tenant_id' => $bill->tenant_id,
            'amount' => $validated['amount'],
            'payment_method' => $validated['payment_method'],
            'payment_date' => now(),
            'transaction_id' => 'TXN-' . uniqid(),
            'status' => 'completed',
            'processed_by' => Auth::id(),
            'notes' => 'Online payment processed',
        ]);

        // Update bill status if fully paid
        $totalPaid = $bill->payments()->where('status', 'completed')->sum('amount');
        if ($totalPaid >= $bill->total_amount) {
            $bill->update(['status' => 'paid']);
        }

        return redirect()->route('bills.show', $bill)
            ->with('success', 'Payment processed successfully!');
    }

    /**
     * Generate payment receipt
     */
    public function generateReceipt(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->load(['bill.tenant', 'bill.unit', 'bill.property', 'processedBy']);

        return view('payments.receipt', compact('payment'));
    }
}
