<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-upload-line text-primary me-2"></i>Upload Document
                        </h4>
                        <p class="text-muted mb-0">
                            Upload and store property documents with expiry tracking
                        </p>
                    </div>
                    <div>
                        <a href="<?php echo e(route('documents.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('documents.store')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Document Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="title" name="title" value="<?php echo e(old('title')); ?>" required>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="description" name="description" rows="3"><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- File Upload -->
                            <div class="mb-3">
                                <label for="file" class="form-label">Document File <span class="text-danger">*</span></label>
                                <input type="file" class="form-control <?php $__errorArgs = ['file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                                <div class="form-text">Supported formats: PDF, DOC, DOCX, JPG, PNG (Max: 10MB)</div>
                                <?php $__errorArgs = ['file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Property and Unit -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($property->id); ?>" 
                                                        <?php echo e(old('property_id', $selectedProperty?->id) == $property->id ? 'selected' : ''); ?>>
                                                    <?php echo e($property->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                            <?php if($selectedProperty): ?>
                                                <?php $__currentLoopData = $selectedProperty->units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($unit->id); ?>" 
                                                            <?php echo e(old('unit_id', $selectedUnit?->id) == $unit->id ? 'selected' : ''); ?>>
                                                        Unit <?php echo e($unit->unit_number); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                        <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Tenant -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Related Tenant</label>
                                <select class="form-select <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="tenant_id" name="tenant_id">
                                    <option value="">Select Tenant (Optional)</option>
                                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($tenant->id); ?>" 
                                                <?php echo e(old('tenant_id') == $tenant->id ? 'selected' : ''); ?>>
                                            <?php echo e($tenant->name); ?> (<?php echo e($tenant->email); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="notes" name="notes" rows="3"><?php echo e(old('notes')); ?></textarea>
                                <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Document Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Document Type <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="contract" <?php echo e(old('type') == 'contract' ? 'selected' : ''); ?>>Contract</option>
                                    <option value="insurance" <?php echo e(old('type') == 'insurance' ? 'selected' : ''); ?>>Insurance</option>
                                    <option value="fire_certificate" <?php echo e(old('type') == 'fire_certificate' ? 'selected' : ''); ?>>Fire Certificate</option>
                                    <option value="lease_agreement" <?php echo e(old('type') == 'lease_agreement' ? 'selected' : ''); ?>>Lease Agreement</option>
                                    <option value="tenant_id" <?php echo e(old('type') == 'tenant_id' ? 'selected' : ''); ?>>Tenant ID</option>
                                    <option value="property_deed" <?php echo e(old('type') == 'property_deed' ? 'selected' : ''); ?>>Property Deed</option>
                                    <option value="permit" <?php echo e(old('type') == 'permit' ? 'selected' : ''); ?>>Permit</option>
                                    <option value="inspection_report" <?php echo e(old('type') == 'inspection_report' ? 'selected' : ''); ?>>Inspection Report</option>
                                    <option value="other" <?php echo e(old('type') == 'other' ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Issue Date -->
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date</label>
                                <input type="date" class="form-control <?php $__errorArgs = ['issue_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="issue_date" name="issue_date" value="<?php echo e(old('issue_date')); ?>">
                                <?php $__errorArgs = ['issue_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Expiry Date -->
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control <?php $__errorArgs = ['expiry_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="expiry_date" name="expiry_date" value="<?php echo e(old('expiry_date')); ?>">
                                <?php $__errorArgs = ['expiry_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Alert Days Before -->
                            <div class="mb-3">
                                <label for="alert_days_before" class="form-label">Alert Days Before Expiry</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['alert_days_before'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="alert_days_before" name="alert_days_before" 
                                       value="<?php echo e(old('alert_days_before', 30)); ?>" min="1" max="365">
                                <div class="form-text">Number of days before expiry to send alert</div>
                                <?php $__errorArgs = ['alert_days_before'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- File Preview -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">File Preview</h6>
                                </div>
                                <div class="card-body text-center" id="file-preview">
                                    <i class="ri-file-line fs-1 text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No file selected</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?php echo e(route('documents.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-upload-line me-1"></i>Upload Document
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        
        // Clear unit options
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = <?php echo json_encode($properties, 15, 512) ?>;
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    });

    // File preview
    const fileInput = document.getElementById('file');
    const filePreview = document.getElementById('file-preview');

    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        
        if (file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
            const fileName = file.name;
            const fileType = file.type;
            
            let icon = 'ri-file-line';
            let iconColor = 'text-muted';
            
            if (fileType.includes('pdf')) {
                icon = 'ri-file-pdf-line';
                iconColor = 'text-danger';
            } else if (fileType.includes('image')) {
                icon = 'ri-image-line';
                iconColor = 'text-info';
            } else if (fileType.includes('word') || fileName.includes('.doc')) {
                icon = 'ri-file-word-line';
                iconColor = 'text-primary';
            }
            
            filePreview.innerHTML = `
                <i class="${icon} fs-1 ${iconColor} mb-2"></i>
                <p class="mb-1"><strong>${fileName}</strong></p>
                <p class="text-muted mb-0">${fileSize} MB</p>
            `;
        } else {
            filePreview.innerHTML = `
                <i class="ri-file-line fs-1 text-muted mb-2"></i>
                <p class="text-muted mb-0">No file selected</p>
            `;
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const file = document.getElementById('file').files[0];
        const issueDate = document.getElementById('issue_date').value;
        const expiryDate = document.getElementById('expiry_date').value;

        // Check file size
        if (file && file.size > 10 * 1024 * 1024) { // 10MB
            e.preventDefault();
            Swal.fire({
                title: 'File Too Large',
                text: 'Please select a file smaller than 10MB.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Check date logic
        if (issueDate && expiryDate && new Date(issueDate) > new Date(expiryDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Dates',
                text: 'Expiry date must be after issue date.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });

    // Auto-fill title based on file name
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        const titleInput = document.getElementById('title');
        
        if (file && !titleInput.value) {
            // Remove file extension and format the name
            const fileName = file.name.replace(/\.[^/.]+$/, "");
            const formattedName = fileName.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            titleInput.value = formattedName;
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/documents/create.blade.php ENDPATH**/ ?>