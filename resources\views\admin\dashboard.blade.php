@extends('layouts.base')

@section('content')
<!-- Welcome Section -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">Welcome to Admin Dashboard, {{ auth()->user()->name }}!</h4>
                        <p class="text-muted mb-0">
                            You have full administrative access to the Property Management System.
                        </p>
                    </div>
                    <div>
                        <form action="{{ route('logout') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="ri-logout-box-line me-1"></i>Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row gx-4 mb-4">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-primary rounded-circle me-3">
                        <div class="icon-box md bg-primary-lighten rounded-5">
                            <i class="ri-building-line fs-4 text-primary"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">{{ \App\Models\Property::count() }}</h2>
                        <p class="m-0">Total Properties</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-success rounded-circle me-3">
                        <div class="icon-box md bg-success-lighten rounded-5">
                            <i class="ri-home-line fs-4 text-success"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">{{ \App\Models\Unit::count() }}</h2>
                        <p class="m-0">Total Units</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-info rounded-circle me-3">
                        <div class="icon-box md bg-info-lighten rounded-5">
                            <i class="ri-group-line fs-4 text-info"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">{{ \App\Models\User::count() }}</h2>
                        <p class="m-0">Total Users</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-warning rounded-circle me-3">
                        <div class="icon-box md bg-warning-lighten rounded-5">
                            <i class="ri-customer-service-line fs-4 text-warning"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">{{ \App\Models\Complaint::count() }}</h2>
                        <p class="m-0">Total Complaints</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('admin.users.create') }}" class="btn btn-outline-primary w-100">
                            <i class="ri-user-add-line me-2"></i>Add New User
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-info w-100">
                            <i class="ri-group-line me-2"></i>Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('admin.roles.create') }}" class="btn btn-outline-success w-100">
                            <i class="ri-shield-user-line me-2"></i>Add New Role
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-warning w-100">
                            <i class="ri-settings-line me-2"></i>Manage Roles
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row gx-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Users</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach(\App\Models\User::latest()->take(5)->get() as $user)
                                <tr>
                                    <td>{{ $user->name }}</td>
                                    <td>{{ $user->email }}</td>
                                    <td>
                                        @foreach($user->roles as $role)
                                            <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                        @endforeach
                                    </td>
                                    <td>{{ $user->created_at->diffForHumans() }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">System Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Available Units</span>
                    <span class="badge bg-success">{{ \App\Models\Unit::where('status', 'available')->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Occupied Units</span>
                    <span class="badge bg-info">{{ \App\Models\Unit::where('status', 'occupied')->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Open Complaints</span>
                    <span class="badge bg-warning">{{ \App\Models\Complaint::where('status', 'open')->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Active Properties</span>
                    <span class="badge bg-primary">{{ \App\Models\Property::where('status', 'active')->count() }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
