<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Document</title>
      <!-- Meta -->
    <meta name="description" content="Marketplace for Bootstrap Admin Dashboards">
    <meta property="og:title" content="Admin Templates - Dashboard Templates">
    <meta property="og:description" content="Marketplace for Bootstrap Admin Dashboards">
    <meta property="og:type" content="Website">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/favicon.svg')); ?>">

    <!-- *************
		************ CSS Files *************
	  ************* -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/remix/remixicon.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/main.min.css')); ?>">

    <!-- *************
		************ Vendor Css Files *************
	  ************ -->

    <!-- Scrollbar CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/overlay-scroll/OverlayScrollbars.min.css')); ?>">

    <!-- Date Range CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/daterange/daterange.css')); ?>">

    <!-- jquert UI -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/jquery-ui/jquery-ui.min.css')); ?>" />

    <!-- Data Tables -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/datatables/dataTables.bs5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/datatables/dataTables.bs5-custom.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/datatables/buttons/dataTables.bs5-custom.css')); ?>">
</head>
<body>
    
 <!-- Page wrapper starts -->
    <div class="page-wrapper">
        <!-- Main container starts -->
        <div class="main-container">

            <!-- Sidebar wrapper starts -->
            <nav id="sidebar" class="sidebar-wrapper">
                <?php echo $__env->make('partials.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </nav>
        <!-- Sidebar wrapper ends -->
            <!-- App container starts -->
            <div class="app-container">
                <!-- Header starts -->
                <div class="app-header d-flex align-items-center">
                    <?php echo $__env->make('partials.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <!-- Header ends -->
                <!-- App hero header starts -->
                <div class="app-hero-header d-flex align-items-center">
                    <?php echo $__env->make('partials.hero-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <!-- App Hero header ends -->
                <!-- App body starts -->
                <div class="app-body">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
                <!-- App body ends -->

                 <!-- App footer starts -->
                <div class="app-footer">
                    <?php echo $__env->make('partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>
      <!-- Main container ends -->
     </div>
    <!-- Page wrapper ends -->






 <!-- *************
			************ JavaScript Files *************
		************* -->
    <!-- Required jQuery first, then Bootstrap Bundle JS -->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>

    <!-- *************
			************ Vendor Js Files *************
		************* -->

    <!-- Overlay Scroll JS -->
    <script src="<?php echo e(asset('assets/vendor/overlay-scroll/jquery.overlayScrollbars.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/overlay-scroll/custom-scrollbar.js')); ?>"></script>

    <!-- Date Range JS -->
    <script src="<?php echo e(asset('assets/vendor/daterange/daterange.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/daterange/custom-daterange.js')); ?>"></script>

    <!-- jquert UI -->
    <script src="<?php echo e(asset('assets/vendor/jquery-ui/jquery-ui.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/jquery-ui/custom.js')); ?>"></script>

    <!-- Apex Charts -->
    <script src="<?php echo e(asset('assets/vendor/apex/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/apex/custom/home/<USER>')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/apex/custom/home/<USER>')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/apex/custom/home/<USER>')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/apex/custom/home/<USER>')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/apex/custom/home/<USER>')); ?>"></script>

    <!-- Data Tables -->
    <script src="<?php echo e(asset('assets/vendor/datatables/dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/datatables/dataTables.bootstrap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/datatables/custom/custom-datatables.js')); ?>"></script>

    <!-- Custom JS files -->
    <script src="<?php echo e(asset('assets/js/custom.js')); ?>"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom SweetAlert Scripts -->
    <?php if(session('success')): ?>
        <script>
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: '<?php echo e(session('success')); ?>',
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        </script>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <script>
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: '<?php echo e(session('error')); ?>',
                timer: 4000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        </script>
    <?php endif; ?>

    <!-- Profile Photo Update Modal -->
    <?php if(auth()->guard()->check()): ?>
    <div class="modal fade" id="profilePhotoModal" tabindex="-1" aria-labelledby="profilePhotoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profilePhotoModalLabel">
                        <i class="ri-camera-line text-primary me-2"></i>Update Profile Photo
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="profilePhotoForm" action="<?php echo e(route('profile.photo.update')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    <div class="modal-body text-center">
                        <!-- Current Photo -->
                        <div class="mb-4">
                            <div class="profile-photo-preview" style="width: 120px; height: 120px; margin: 0 auto;">
                                <img id="modal-photo-preview"
                                     src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                                     alt="Profile Preview"
                                     class="rounded-circle border border-primary"
                                     style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <h6 class="mt-2 mb-0"><?php echo e(auth()->user()->name); ?></h6>
                            <small class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User'))); ?></small>
                        </div>

                        <!-- File Input -->
                        <div class="mb-3">
                            <input type="file" class="form-control"
                                   id="modal_profile_photo" name="profile_photo" accept="image/*"
                                   onchange="previewModalProfilePhoto(this)" required>
                            <div class="form-text">
                                <i class="ri-information-line"></i> Upload a new profile photo (JPG, PNG, GIF - Max: 2MB)
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="ri-close-line me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-upload-line me-1"></i>Update Photo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script>
        // Show profile photo modal
        function showProfilePhotoModal() {
            <?php if(auth()->guard()->check()): ?>
                const modal = new bootstrap.Modal(document.getElementById('profilePhotoModal'));
                modal.show();
            <?php endif; ?>
        }

        // Preview profile photo in modal
        function previewModalProfilePhoto(input) {
            const preview = document.getElementById('modal-photo-preview');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.src = e.target.result;
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Handle profile photo form submission
        <?php if(auth()->guard()->check()): ?>
        document.getElementById('profilePhotoForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="ri-loader-4-line me-1"></i>Updating...';
            submitBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update sidebar photo
                    const sidebarImg = document.querySelector('.sidebar-profile img');
                    if (sidebarImg) {
                        sidebarImg.src = data.photo_url + '?t=' + new Date().getTime();
                    }

                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('profilePhotoModal')).hide();

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: 'Profile photo updated successfully!',
                        timer: 3000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });

                    // Reload page to update all photos
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    throw new Error(data.message || 'Failed to update profile photo');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: error.message || 'Failed to update profile photo',
                    timer: 4000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            })
            .finally(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
        <?php endif; ?>
    </script>

    <!-- Logout Confirmation Script -->
    <script>
        function confirmLogout() {
            Swal.fire({
                title: 'Logout Confirmation',
                html: `
                    <div class="text-center">
                        <i class="ri-logout-circle-line fs-1 text-warning mb-3"></i>
                        <p class="mb-2">Are you sure you want to logout?</p>
                        <small class="text-muted">You will be redirected to the login page.</small>
                    </div>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-logout-circle-line me-1"></i>Yes, Logout',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true,
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false,
                allowOutsideClick: false,
                allowEscapeKey: true,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Logging out...',
                        html: '<div class="text-center"><i class="ri-loader-4-line fs-2 text-primary"></i><br><small class="text-muted">Please wait while we log you out safely.</small></div>',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        customClass: {
                            popup: 'swal2-popup-custom'
                        }
                    });

                    // Submit the logout form
                    setTimeout(() => {
                        document.getElementById('logoutForm').submit();
                    }, 1000);
                }
            });
        }
    </script>

    <!-- Custom SweetAlert Styles -->
    <style>
        .swal2-popup-custom {
            border-radius: 15px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
        }

        .swal2-title-custom {
            color: #495057 !important;
            font-weight: 600 !important;
        }

        .swal2-html-container {
            color: #6c757d !important;
        }

        .swal2-icon.swal2-question {
            border-color: #ffc107 !important;
            color: #ffc107 !important;
        }
    </style>

    <?php echo $__env->yieldContent('scripts'); ?>
  </body>
</body>
</html><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/layouts/base.blade.php ENDPATH**/ ?>