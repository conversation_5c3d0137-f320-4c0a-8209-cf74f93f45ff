<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-bar-chart-line text-primary me-2"></i>Property Reports
                        </h4>
                        <p class="text-muted mb-0">
                            Comprehensive property performance and analytics dashboard
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportReport('pdf')">
                            <i class="ri-file-pdf-line me-1"></i>Export PDF
                        </button>
                        <button class="btn btn-outline-success" onclick="exportReport('excel')">
                            <i class="ri-file-excel-line me-1"></i>Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Occupancy Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ri-home-line me-2"></i>Occupancy Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1"><?php echo e($occupancyStats['total_units']); ?></h3>
                                                <p class="mb-0">Total Units</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-building-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1"><?php echo e($occupancyStats['occupied_units']); ?></h3>
                                                <p class="mb-0">Occupied Units</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-user-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1"><?php echo e($occupancyStats['available_units']); ?></h3>
                                                <p class="mb-0">Available Units</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-home-2-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1"><?php echo e($occupancyStats['occupancy_rate']); ?>%</h3>
                                                <p class="mb-0">Occupancy Rate</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-pie-chart-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ri-money-dollar-circle-line me-2"></i>Financial Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">$<?php echo e(number_format($financialStats['total_rent'], 0)); ?></h3>
                                                <p class="mb-0">Total Rent</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-money-dollar-circle-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">$<?php echo e(number_format($financialStats['collected_rent'], 0)); ?></h3>
                                                <p class="mb-0">Collected</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-check-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">$<?php echo e(number_format($financialStats['pending_rent'], 0)); ?></h3>
                                                <p class="mb-0">Pending</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-time-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1"><?php echo e($financialStats['collection_rate']); ?>%</h3>
                                                <p class="mb-0">Collection Rate</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-percent-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ri-tools-line me-2"></i>Maintenance & Complaints
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['total_tasks']); ?></h3>
                                        <p class="mb-0">Total Tasks</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['completed_tasks']); ?></h3>
                                        <p class="mb-0">Completed</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['pending_tasks']); ?></h3>
                                        <p class="mb-0">Pending</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['overdue_tasks']); ?></h3>
                                        <p class="mb-0">Overdue</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['total_complaints']); ?></h3>
                                        <p class="mb-0">Complaints</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-4 mb-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h3 class="mb-1"><?php echo e($maintenanceStats['completion_rate']); ?>%</h3>
                                        <p class="mb-0">Completion Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Details Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ri-building-line me-2"></i>Property Details
                </h5>
            </div>
            <div class="card-body">
                <?php if($properties->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Property</th>
                                    <th>Total Units</th>
                                    <th>Occupied</th>
                                    <th>Available</th>
                                    <th>Occupancy Rate</th>
                                    <th>Monthly Rent</th>
                                    <th>Collection Rate</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $totalUnits = $property->units->count();
                                        $occupiedUnits = $property->units->where('status', 'occupied')->count();
                                        $availableUnits = $property->units->where('status', 'available')->count();
                                        $occupancyRate = $totalUnits > 0 ? ($occupiedUnits / $totalUnits) * 100 : 0;
                                        $monthlyRent = $property->units->where('status', 'occupied')->sum('rent_amount');
                                        
                                        // Calculate collection rate for this property
                                        $totalBills = $property->bills->whereIn('status', ['pending', 'paid', 'overdue'])->sum('amount');
                                        $paidBills = $property->bills->where('status', 'paid')->sum('amount');
                                        $collectionRate = $totalBills > 0 ? ($paidBills / $totalBills) * 100 : 100;
                                    ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($property->name); ?></strong>
                                                <br><small class="text-muted"><?php echo e($property->address); ?></small>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary"><?php echo e($totalUnits); ?></span></td>
                                        <td><span class="badge bg-success"><?php echo e($occupiedUnits); ?></span></td>
                                        <td><span class="badge bg-info"><?php echo e($availableUnits); ?></span></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: <?php echo e($occupancyRate); ?>%">
                                                    <?php echo e(round($occupancyRate, 1)); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td><strong>$<?php echo e(number_format($monthlyRent, 0)); ?></strong></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: <?php echo e($collectionRate); ?>%">
                                                    <?php echo e(round($collectionRate, 1)); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('properties.show', $property)); ?>" class="btn btn-outline-primary" title="View Details">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <button class="btn btn-outline-info" onclick="viewPropertyReport(<?php echo e($property->id); ?>)" title="Detailed Report">
                                                    <i class="ri-file-chart-line"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-building-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Properties Found</h5>
                        <p class="text-muted">No properties available for reporting.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function exportReport(format) {
    Swal.fire({
        title: 'Export Report',
        text: `Export property report as ${format.toUpperCase()}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Export',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implement export functionality
            Swal.fire('Coming Soon', 'Export functionality will be available soon.', 'info');
        }
    });
}

function viewPropertyReport(propertyId) {
    // Implement detailed property report view
    window.location.href = `/properties/${propertyId}`;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/reports/property.blade.php ENDPATH**/ ?>