<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Notification::forUser($user->id)->with(['createdBy', 'relatedTask', 'relatedProperty', 'relatedUnit']);

        // Apply filters
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        if ($request->filled('priority')) {
            $query->byPriority($request->priority);
        }

        if ($request->filled('status')) {
            if ($request->status === 'read') {
                $query->read();
            } elseif ($request->status === 'unread') {
                $query->unread();
            }
        }

        $notifications = $query->latest()->paginate(20);

        // Calculate statistics
        $stats = [
            'total' => Notification::forUser($user->id)->count(),
            'unread' => Notification::forUser($user->id)->unread()->count(),
            'read' => Notification::forUser($user->id)->read()->count(),
            'recent' => Notification::forUser($user->id)->recent(7)->count(),
        ];

        return view('notifications.index', compact('notifications', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Notification::class);

        $user = Auth::user();

        // Get users based on role
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('admin', $userRoles)) {
            $users = User::where('is_active', true)->get();
        } elseif (in_array('property_owner', $userRoles)) {
            // Property owners can notify users related to their properties
            $users = collect();

            // Get all active users except other admins and property owners
            $users = User::where('is_active', true)
                ->whereHas('roles', function($q) {
                    $q->whereIn('name', ['tenant', 'maintenance_staff', 'receptionist', 'property_manager']);
                })->get();

            // If no users found, get all active users
            if ($users->isEmpty()) {
                $users = User::where('is_active', true)->get();
            }
        } else {
            $users = collect();
        }

        return view('notifications.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Notification::class);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:task_reminder,lease_expiry,document_expiry,payment_due,maintenance_request,system_alert,general',
            'priority' => 'required|in:low,medium,high,urgent',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'scheduled_for' => 'nullable|date|after:now',
            'send_email' => 'boolean',
        ]);

        $notifications = [];
        foreach ($validated['user_ids'] as $userId) {
            $notification = Notification::create([
                'title' => $validated['title'],
                'message' => $validated['message'],
                'type' => $validated['type'],
                'priority' => $validated['priority'],
                'user_id' => $userId,
                'created_by' => Auth::id(),
                'scheduled_for' => $validated['scheduled_for'] ?? null,
            ]);

            $notifications[] = $notification;

            // Send email if requested and not scheduled
            if ($request->boolean('send_email') && !$validated['scheduled_for']) {
                // Here you would implement email sending logic
                $notification->markEmailAsSent();
            }
        }

        $count = count($notifications);
        return redirect()->route('notifications.index')
            ->with('success', "Created {$count} notification(s) successfully!");
    }

    /**
     * Display the specified resource.
     */
    public function show(Notification $notification)
    {
        $this->authorize('view', $notification);

        // Mark as read if it's the user's notification
        if ($notification->user_id === Auth::id() && !$notification->is_read) {
            $notification->markAsRead();
        }

        $notification->load(['createdBy', 'relatedTask', 'relatedProperty', 'relatedUnit']);

        return view('notifications.show', compact('notification'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Notification $notification)
    {
        $this->authorize('delete', $notification);

        $notification->delete();

        return redirect()->route('notifications.index')
            ->with('success', 'Notification deleted successfully!');
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification)
    {
        $this->authorize('view', $notification);

        $notification->markAsRead();

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        
        Notification::forUser($user->id)->unread()->update([
            'is_read' => true,
            'read_at' => now(),
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount()
    {
        $user = Auth::user();
        $count = Notification::forUser($user->id)->unread()->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications for header dropdown
     */
    public function getRecent()
    {
        $user = Auth::user();
        
        $notifications = Notification::forUser($user->id)
            ->with(['relatedTask', 'relatedProperty'])
            ->latest()
            ->take(10)
            ->get();

        return response()->json($notifications);
    }

    /**
     * Bulk actions on notifications
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:mark_read,mark_unread,delete',
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'exists:notifications,id',
        ]);

        $user = Auth::user();
        $notifications = Notification::whereIn('id', $validated['notification_ids'])
            ->forUser($user->id)
            ->get();

        $count = 0;
        foreach ($notifications as $notification) {
            switch ($validated['action']) {
                case 'mark_read':
                    if (!$notification->is_read) {
                        $notification->markAsRead();
                        $count++;
                    }
                    break;
                case 'mark_unread':
                    if ($notification->is_read) {
                        $notification->update(['is_read' => false, 'read_at' => null]);
                        $count++;
                    }
                    break;
                case 'delete':
                    $notification->delete();
                    $count++;
                    break;
            }
        }

        $actionText = [
            'mark_read' => 'marked as read',
            'mark_unread' => 'marked as unread',
            'delete' => 'deleted',
        ];

        return redirect()->route('notifications.index')
            ->with('success', "{$count} notification(s) {$actionText[$validated['action']]} successfully!");
    }

    /**
     * Send scheduled notifications
     */
    public function sendScheduled()
    {
        $notifications = Notification::scheduled()->due()->get();

        foreach ($notifications as $notification) {
            // Here you would implement email sending logic
            $notification->markEmailAsSent();
        }

        return response()->json([
            'success' => true,
            'sent' => $notifications->count(),
        ]);
    }

    /**
     * Create system notifications for lease expiry
     */
    public static function createLeaseExpiryNotifications()
    {
        $units = \App\Models\Unit::whereNotNull('lease_end')
            ->where('lease_end', '<=', now()->addDays(30))
            ->where('lease_end', '>=', now())
            ->with(['property.owner', 'tenant'])
            ->get();

        foreach ($units as $unit) {
            // Notify property owner
            if ($unit->property->owner) {
                Notification::createLeaseExpiryAlert($unit, $unit->property->owner);
            }

            // Notify tenant
            if ($unit->tenant) {
                Notification::create([
                    'title' => 'Lease Expiry Notice',
                    'message' => "Your lease for Unit {$unit->unit_number} is expiring on {$unit->lease_end->format('M d, Y')}",
                    'type' => 'lease_expiry',
                    'priority' => 'high',
                    'user_id' => $unit->tenant->id,
                    'related_property_id' => $unit->property_id,
                    'related_unit_id' => $unit->id,
                ]);
            }
        }
    }

    /**
     * Create system notifications for document expiry
     */
    public static function createDocumentExpiryNotifications()
    {
        $documents = \App\Models\Document::needsAlert()->with(['property.owner', 'uploadedBy'])->get();

        foreach ($documents as $document) {
            // Notify property owner
            if ($document->property && $document->property->owner) {
                Notification::createDocumentExpiryAlert($document, $document->property->owner);
            }

            // Notify uploader
            if ($document->uploadedBy) {
                Notification::createDocumentExpiryAlert($document, $document->uploadedBy);
            }

            $document->markExpiryAlertSent();
        }
    }
}
