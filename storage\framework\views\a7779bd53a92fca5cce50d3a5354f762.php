<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">Welcome to Property Owner Dashboard, <?php echo e(auth()->user()->name); ?>!</h4>
                        <p class="text-muted mb-0">
                            Manage your properties, view occupancy rates, and track financial performance.
                        </p>
                    </div>
                    <div>
                      
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Property Owner Statistics Row 1 -->
<div class="row gx-4 mb-4">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-primary rounded-circle me-3">
                        <div class="icon-box md bg-primary-lighten rounded-5">
                            <i class="ri-building-line fs-4 text-primary"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(auth()->user()->ownedProperties->count()); ?></h2>
                        <p class="m-0">My Properties</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-success rounded-circle me-3">
                        <div class="icon-box md bg-success-lighten rounded-5">
                            <i class="ri-home-line fs-4 text-success"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $propertyIds = auth()->user()->ownedProperties->pluck('id');
                            $totalUnits = \App\Models\Unit::whereIn('property_id', $propertyIds)->count();
                        ?>
                        <h2 class="lh-1"><?php echo e($totalUnits); ?></h2>
                        <p class="m-0">Total Units</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-info rounded-circle me-3">
                        <div class="icon-box md bg-info-lighten rounded-5">
                            <i class="ri-group-line fs-4 text-info"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $occupiedUnits = \App\Models\Unit::whereIn('property_id', $propertyIds)->where('status', 'occupied')->count();
                        ?>
                        <h2 class="lh-1"><?php echo e($occupiedUnits); ?></h2>
                        <p class="m-0">Total Residents</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-warning rounded-circle me-3">
                        <div class="icon-box md bg-warning-lighten rounded-5">
                            <i class="ri-customer-service-line fs-4 text-warning"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $openComplaints = \App\Models\Complaint::whereIn('property_id', $propertyIds)->where('status', '!=', 'resolved')->count();
                        ?>
                        <h2 class="lh-1"><?php echo e($openComplaints); ?></h2>
                        <p class="m-0">Open Complaints</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Property Owner Statistics Row 2 -->
<div class="row gx-4 mb-4">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-purple rounded-circle me-3">
                        <div class="icon-box md bg-purple-lighten rounded-5">
                            <i class="ri-user-follow-line fs-4 text-purple"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $todayVisitors = \App\Models\Visitor::whereIn('property_id', $propertyIds)->whereDate('check_in_time', today())->count();
                        ?>
                        <h2 class="lh-1"><?php echo e($todayVisitors); ?></h2>
                        <p class="m-0">Today's Visitors</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-danger rounded-circle me-3">
                        <div class="icon-box md bg-danger-lighten rounded-5">
                            <i class="ri-user-forbid-line fs-4 text-danger"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $blacklistedVisitors = \App\Models\BlacklistedVisitor::whereIn('property_id', $propertyIds)->where('is_active', true)->count();
                        ?>
                        <h2 class="lh-1"><?php echo e($blacklistedVisitors); ?></h2>
                        <p class="m-0">Blacklisted Visitors</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-success rounded-circle me-3">
                        <div class="icon-box md bg-success-lighten rounded-5">
                            <i class="ri-money-dollar-circle-line fs-4 text-success"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $totalBilled = \App\Models\Bill::whereIn('property_id', $propertyIds)->sum('total_amount');
                        ?>
                        <h2 class="lh-1">$<?php echo e(number_format($totalBilled, 0)); ?></h2>
                        <p class="m-0">Total Billed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-info rounded-circle me-3">
                        <div class="icon-box md bg-info-lighten rounded-5">
                            <i class="ri-money-dollar-box-line fs-4 text-info"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <?php
                            $amountReceivable = \App\Models\Bill::whereIn('property_id', $propertyIds)->where('status', 'unpaid')->sum('total_amount');
                        ?>
                        <h2 class="lh-1">$<?php echo e(number_format($amountReceivable, 0)); ?></h2>
                        <p class="m-0">Amount Receivable</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Management -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ri-team-line text-primary me-2"></i>My Employees
                </h5>
                <div>
                    <a href="<?php echo e(route('owner.users.index')); ?>" class="btn btn-outline-primary me-2">
                        <i class="ri-list-check me-1"></i>View All
                    </a>
                    <a href="<?php echo e(route('owner.users.create')); ?>" class="btn btn-primary">
                        <i class="ri-user-add-line me-1"></i>Add Employee
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php
                    $employees = auth()->user()->employees()->with('roles')->latest()->take(6)->get();
                    $employeesByRole = $employees->groupBy(function ($user) {
                        return $user->roles->first()->name ?? 'no_role';
                    });
                ?>

                <?php if($employees->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $employeesByRole; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $roleName => $roleEmployees): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-12 mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="ri-user-line me-1"></i><?php echo e(ucfirst(str_replace('_', ' ', $roleName))); ?>

                                    <span class="badge bg-primary ms-2"><?php echo e($roleEmployees->count()); ?></span>
                                </h6>
                                <div class="row">
                                    <?php $__currentLoopData = $roleEmployees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card border h-100">
                                                <div class="card-body p-3">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <div class="avatar-sm me-2" style="width: 40px; height: 40px;">
                                                            <img src="<?php echo e($employee->profile_photo_url); ?>"
                                                                 alt="<?php echo e($employee->name); ?>"
                                                                 class="rounded-circle border"
                                                                 style="width: 100%; height: 100%; object-fit: cover;">
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-0"><?php echo e($employee->name); ?></h6>
                                                            <small class="text-muted"><?php echo e($employee->email); ?></small>
                                                        </div>
                                                        <?php if($employee->is_active): ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Inactive</span>
                                                        <?php endif; ?>
                                                    </div>

                                                    <?php if($employee->phone): ?>
                                                        <p class="mb-2 small">
                                                            <i class="ri-phone-line me-1"></i><?php echo e($employee->phone); ?>

                                                        </p>
                                                    <?php endif; ?>

                                                    <p class="mb-2 small">
                                                        <i class="ri-calendar-line me-1"></i>Joined <?php echo e($employee->created_at->format('M d, Y')); ?>

                                                    </p>

                                                    <div class="d-flex gap-1">
                                                        <a href="<?php echo e(route('owner.users.show', $employee)); ?>" class="btn btn-outline-info btn-sm">
                                                            <i class="ri-eye-line"></i>
                                                        </a>
                                                        <a href="<?php echo e(route('owner.users.edit', $employee)); ?>" class="btn btn-outline-warning btn-sm">
                                                            <i class="ri-edit-line"></i>
                                                        </a>
                                                        <?php if($employee->is_active): ?>
                                                            <button type="button" class="btn btn-outline-secondary btn-sm deactivate-user"
                                                                    data-user-name="<?php echo e($employee->name); ?>"
                                                                    data-url="<?php echo e(route('owner.users.deactivate', $employee)); ?>">
                                                                <i class="ri-pause-circle-line"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-success btn-sm activate-user"
                                                                    data-user-name="<?php echo e($employee->name); ?>"
                                                                    data-url="<?php echo e(route('owner.users.activate', $employee)); ?>">
                                                                <i class="ri-play-circle-line"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if(auth()->user()->employees->count() > 6): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('owner.users.index')); ?>" class="btn btn-outline-primary">
                                <i class="ri-arrow-right-line me-1"></i>View All <?php echo e(auth()->user()->employees->count()); ?> Employees
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-group-line fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No Employees Yet</h5>
                        <p class="text-muted">Start building your team by adding your first employee.</p>
                        <a href="<?php echo e(route('owner.users.create')); ?>" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Add First Employee
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Property Owner Actions -->
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Property Owner Features</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-building-line fs-1 text-success mb-3"></i>
                                <h6>Property Management</h6>
                                <p class="text-muted small">Manage your properties, units, and occupancy</p>
                                <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-success btn-sm">
                                    <i class="ri-building-line me-1"></i>Manage Properties
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-group-line fs-1 text-primary mb-3"></i>
                                <h6>Employee Management</h6>
                                <p class="text-muted small">Manage your condominium staff and employees</p>
                                <a href="<?php echo e(route('owner.users.index')); ?>" class="btn btn-primary btn-sm">
                                    <i class="ri-team-line me-1"></i>Manage Staff
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-home-line fs-1 text-success mb-3"></i>
                                <h6>Unit Management</h6>
                                <p class="text-muted small">Track occupancy and manage unit details</p>
                                <button class="btn btn-outline-success btn-sm">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-line-chart-line fs-1 text-info mb-3"></i>
                                <h6>Financial Reports</h6>
                                <p class="text-muted small">View revenue, expenses, and profitability</p>
                                <button class="btn btn-outline-info btn-sm">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activate User
    document.querySelectorAll('.activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Activate Employee Account',
                html: `Are you sure you want to <strong>activate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-play-circle-line me-1"></i>Yes, Activate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Deactivate User
    document.querySelectorAll('.deactivate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Deactivate Employee Account',
                html: `Are you sure you want to <strong>deactivate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-pause-circle-line me-1"></i>Yes, Deactivate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/owner/dashboard.blade.php ENDPATH**/ ?>