<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'type',
        'priority',
        'user_id',
        'created_by',
        'related_task_id',
        'related_property_id',
        'related_unit_id',
        'data',
        'is_read',
        'is_email_sent',
        'read_at',
        'email_sent_at',
        'scheduled_for',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'is_email_sent' => 'boolean',
        'read_at' => 'datetime',
        'email_sent_at' => 'datetime',
        'scheduled_for' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function relatedTask(): BelongsTo
    {
        return $this->belongsTo(Task::class, 'related_task_id');
    }

    public function relatedProperty(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'related_property_id');
    }

    public function relatedUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'related_unit_id');
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_for');
    }

    public function scopeDue($query)
    {
        return $query->where('scheduled_for', '<=', now());
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getIsScheduledAttribute(): bool
    {
        return !is_null($this->scheduled_for);
    }

    public function getIsDueAttribute(): bool
    {
        return $this->scheduled_for && $this->scheduled_for->isPast();
    }

    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    // Methods
    public function markAsRead(): bool
    {
        return $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    public function markEmailAsSent(): bool
    {
        return $this->update([
            'is_email_sent' => true,
            'email_sent_at' => now(),
        ]);
    }

    public static function createTaskReminder(Task $task, User $user, string $message = null): self
    {
        return self::create([
            'title' => 'Task Reminder: ' . $task->title,
            'message' => $message ?? "You have a task due: {$task->title}",
            'type' => 'task_reminder',
            'priority' => $task->priority,
            'user_id' => $user->id,
            'related_task_id' => $task->id,
            'related_property_id' => $task->property_id,
            'related_unit_id' => $task->unit_id,
        ]);
    }

    public static function createLeaseExpiryAlert(Unit $unit, User $user): self
    {
        return self::create([
            'title' => 'Lease Expiry Alert',
            'message' => "Lease for Unit {$unit->unit_number} is expiring on {$unit->lease_end->format('M d, Y')}",
            'type' => 'lease_expiry',
            'priority' => 'high',
            'user_id' => $user->id,
            'related_property_id' => $unit->property_id,
            'related_unit_id' => $unit->id,
            'data' => [
                'lease_end_date' => $unit->lease_end,
                'tenant_name' => $unit->tenant->name ?? null,
            ],
        ]);
    }

    public static function createDocumentExpiryAlert(Document $document, User $user): self
    {
        return self::create([
            'title' => 'Document Expiry Alert',
            'message' => "Document '{$document->title}' is expiring on {$document->expiry_date->format('M d, Y')}",
            'type' => 'document_expiry',
            'priority' => 'medium',
            'user_id' => $user->id,
            'related_property_id' => $document->property_id,
            'related_unit_id' => $document->unit_id,
            'data' => [
                'document_id' => $document->id,
                'document_type' => $document->type,
                'expiry_date' => $document->expiry_date,
            ],
        ]);
    }
}
