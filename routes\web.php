<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Language switching route (available to all users)
Route::get('/language/{locale}', function ($locale) {
    if (in_array($locale, ['en', 'id'])) {
        session(['locale' => $locale]);
    }
    return redirect()->back();
})->name('language.switch');

// Authentication Routes
Route::get('/', [LoginController::class, 'showLoginForm'])->name('login');
Route::get('/login', [LoginController::class, 'showLoginForm']);
Route::post('/login', [LoginController::class, 'login'])->name('login.submit');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Protected Routes (require authentication and active account)
Route::middleware(['auth', 'active'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'dashboardView'])->name('dashboard');

    // Profile photo update route
    Route::patch('/profile/photo', [\App\Http\Controllers\ProfileController::class, 'updatePhoto'])->name('profile.photo.update');

    // Profile routes
    Route::get('/profile', [\App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [\App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [\App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');

    // Settings route (placeholder)
    Route::get('/settings', function() {
        return redirect()->route('profile.edit')->with('info', 'Settings page coming soon. Use profile edit for now.');
    })->name('settings');

    // Admin Routes
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', function () {
            return view('admin.dashboard');
        })->name('dashboard');

        // User Management
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        Route::patch('users/{user}/activate', [\App\Http\Controllers\Admin\UserController::class, 'activate'])->name('users.activate');
        Route::patch('users/{user}/deactivate', [\App\Http\Controllers\Admin\UserController::class, 'deactivate'])->name('users.deactivate');
        Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

        // Role Management
        Route::resource('roles', \App\Http\Controllers\Admin\RoleController::class);
    });

    // Property Management Routes (for both admin and property owners)
    Route::resource('properties', \App\Http\Controllers\PropertyController::class);

    // Unit Management Routes
    Route::resource('units', \App\Http\Controllers\UnitController::class);
    Route::post('units/{unit}/assign-tenant', [\App\Http\Controllers\UnitController::class, 'assignTenant'])->name('units.assign-tenant');
    Route::delete('units/{unit}/remove-tenant', [\App\Http\Controllers\UnitController::class, 'removeTenant'])->name('units.remove-tenant');

    // Billing & Payment Routes
    Route::resource('bills', \App\Http\Controllers\BillController::class);
    Route::post('bills/generate-rent', [\App\Http\Controllers\BillController::class, 'generateRentBills'])->name('bills.generate-rent');
    Route::patch('bills/{bill}/mark-paid', [\App\Http\Controllers\BillController::class, 'markAsPaid'])->name('bills.mark-paid');
    Route::post('bills/{bill}/send-reminder', [\App\Http\Controllers\BillController::class, 'sendReminder'])->name('bills.send-reminder');

    Route::resource('payments', \App\Http\Controllers\PaymentController::class);
    Route::post('payments/process-online/{bill}', [\App\Http\Controllers\PaymentController::class, 'processOnlinePayment'])->name('payments.process-online');
    Route::get('payments/{payment}/receipt', [\App\Http\Controllers\PaymentController::class, 'generateReceipt'])->name('payments.receipt');

    // Complaint & Maintenance Routes
    Route::resource('complaints', \App\Http\Controllers\ComplaintController::class);
    Route::post('complaints/{complaint}/assign', [\App\Http\Controllers\ComplaintController::class, 'assign'])->name('complaints.assign');
    Route::patch('complaints/{complaint}/status', [\App\Http\Controllers\ComplaintController::class, 'updateStatus'])->name('complaints.update-status');
    Route::get('complaints/{complaint}/attachment/{index}', [\App\Http\Controllers\ComplaintController::class, 'downloadAttachment'])->name('complaints.download-attachment');

    // Task & Notification System Routes
    Route::resource('tasks', \App\Http\Controllers\TaskController::class);
    Route::patch('tasks/{task}/complete', [\App\Http\Controllers\TaskController::class, 'markCompleted'])->name('tasks.complete');
    Route::get('tasks-dashboard', [\App\Http\Controllers\TaskController::class, 'dashboard'])->name('tasks.dashboard');

    // Specific notification routes (must be before resource route)
    Route::get('notifications/recent', [\App\Http\Controllers\NotificationController::class, 'getRecent'])->name('notifications.recent');
    Route::get('notifications/unread-count', [\App\Http\Controllers\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::patch('notifications/{notification}/read', [\App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.mark-read');
    Route::patch('notifications/mark-all-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::post('notifications/bulk-action', [\App\Http\Controllers\NotificationController::class, 'bulkAction'])->name('notifications.bulk-action');
    Route::resource('notifications', \App\Http\Controllers\NotificationController::class)->except(['edit', 'update']);

    // Document Management Routes
    Route::resource('documents', \App\Http\Controllers\DocumentController::class);
    Route::get('documents/{document}/download', [\App\Http\Controllers\DocumentController::class, 'download'])->name('documents.download');
    Route::get('documents/{document}/view', [\App\Http\Controllers\DocumentController::class, 'view'])->name('documents.view');
    Route::get('documents-expiring', [\App\Http\Controllers\DocumentController::class, 'expiring'])->name('documents.expiring');
    Route::post('documents/check-expired', [\App\Http\Controllers\DocumentController::class, 'checkExpired'])->name('documents.check-expired');
    Route::post('documents/send-expiry-alerts', [\App\Http\Controllers\DocumentController::class, 'sendExpiryAlerts'])->name('documents.send-expiry-alerts');

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('property', [\App\Http\Controllers\ReportController::class, 'propertyReports'])->name('property');
        Route::get('financial', [\App\Http\Controllers\ReportController::class, 'financialReports'])->name('financial');
        Route::post('property/export', [\App\Http\Controllers\ReportController::class, 'exportPropertyReport'])->name('property.export');
        Route::post('financial/export', [\App\Http\Controllers\ReportController::class, 'exportFinancialReport'])->name('financial.export');
    });

    // Visitor Management Routes
    Route::resource('visitors', \App\Http\Controllers\VisitorController::class);
    Route::get('visitors-dashboard', [\App\Http\Controllers\VisitorController::class, 'dashboard'])->name('visitors.dashboard');
    Route::patch('visitors/{visitor}/check-out', [\App\Http\Controllers\VisitorController::class, 'checkOut'])->name('visitors.check-out');
    Route::post('visitors/quick-check-in', [\App\Http\Controllers\VisitorController::class, 'quickCheckIn'])->name('visitors.quick-check-in');



    // Property Owner Routes
    Route::middleware(['role:property_owner'])->prefix('owner')->name('owner.')->group(function () {
        Route::get('/dashboard', function () {
            return view('owner.dashboard');
        })->name('dashboard');

        // User Management for Property Owners
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        Route::patch('users/{user}/activate', [\App\Http\Controllers\Admin\UserController::class, 'activate'])->name('users.activate');
        Route::patch('users/{user}/deactivate', [\App\Http\Controllers\Admin\UserController::class, 'deactivate'])->name('users.deactivate');
        Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    });

    // Tenant Routes
    Route::middleware(['role:tenant'])->prefix('tenant')->name('tenant.')->group(function () {
        Route::get('/dashboard', function () {
            return view('tenant.dashboard');
        })->name('dashboard');
    });

    // Receptionist Routes
    Route::middleware(['role:receptionist'])->prefix('receptionist')->name('receptionist.')->group(function () {
        Route::get('/dashboard', function () {
            return view('receptionist.dashboard');
        })->name('dashboard');
    });

    // Maintenance Routes
    Route::middleware(['role:maintenance_staff'])->prefix('maintenance')->name('maintenance.')->group(function () {
        Route::get('/dashboard', function () {
            return view('maintenance.dashboard');
        })->name('dashboard');
    });
});