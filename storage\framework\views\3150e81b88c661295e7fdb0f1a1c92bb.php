<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-bill-line text-primary me-2"></i>Bills Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage all bills and invoices for properties and tenants
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Bill::class)): ?>
                            <a href="<?php echo e(route('bills.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Create Bill
                            </a>
                        <?php endif; ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('bills.index')); ?>">All Bills</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('bills.index', ['status' => 'pending'])); ?>">Pending</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('bills.index', ['status' => 'paid'])); ?>">Paid</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('bills.index', ['status' => 'overdue'])); ?>">Overdue</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('bills.index', ['status' => 'cancelled'])); ?>">Cancelled</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format($stats['total_amount'] ?? 0, 2)); ?></h3>
                                <p class="mb-0">Total Amount</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-money-dollar-circle-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['pending_count'] ?? 0); ?></h3>
                                <p class="mb-0">Pending Bills</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format($stats['paid_amount'] ?? 0, 2)); ?></h3>
                                <p class="mb-0">Paid Amount</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-check-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['overdue_count'] ?? 0); ?></h3>
                                <p class="mb-0">Overdue Bills</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bills Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Bills List</h5>
                    <div class="d-flex gap-2">
                        <form method="GET" action="<?php echo e(route('bills.index')); ?>" class="d-flex gap-2">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search bills..." value="<?php echo e(request('search')); ?>">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if(isset($bills) && $bills->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Bill #</th>
                                    <th>Tenant</th>
                                    <th>Property/Unit</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $bills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong>#<?php echo e($bill->bill_number ?? 'B-' . str_pad($bill->id, 6, '0', STR_PAD_LEFT)); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($bill->tenant): ?>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        <?php if($bill->tenant->profile_photo): ?>
                                                            <img src="<?php echo e(asset('storage/' . $bill->tenant->profile_photo)); ?>" 
                                                                 alt="<?php echo e($bill->tenant->name); ?>" class="rounded-circle">
                                                        <?php else: ?>
                                                            <div class="avatar-initial rounded-circle bg-primary">
                                                                <?php echo e(substr($bill->tenant->name, 0, 1)); ?>

                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo e($bill->tenant->name); ?></h6>
                                                        <small class="text-muted"><?php echo e($bill->tenant->email); ?></small>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">No tenant</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($bill->unit): ?>
                                                <div>
                                                    <strong><?php echo e($bill->unit->property->name ?? 'Unknown Property'); ?></strong>
                                                    <br><small class="text-muted">Unit <?php echo e($bill->unit->unit_number); ?></small>
                                                </div>
                                            <?php elseif($bill->property): ?>
                                                <strong><?php echo e($bill->property->name); ?></strong>
                                            <?php else: ?>
                                                <span class="text-muted">No property</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $bill->type ?? 'general'))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <strong>$<?php echo e(number_format($bill->amount ?? 0, 2)); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($bill->due_date): ?>
                                                <span class="<?php echo e($bill->is_overdue ? 'text-danger' : ($bill->is_due_soon ? 'text-warning' : 'text-muted')); ?>">
                                                    <?php echo e($bill->due_date->format('M d, Y')); ?>

                                                </span>
                                                <?php if($bill->is_overdue): ?>
                                                    <br><small class="text-danger"><?php echo e($bill->days_overdue); ?> days overdue</small>
                                                <?php elseif($bill->is_due_soon): ?>
                                                    <br><small class="text-warning">Due in <?php echo e($bill->days_until_due); ?> days</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">No due date</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'paid' => 'success',
                                                    'overdue' => 'danger',
                                                    'cancelled' => 'secondary',
                                                    'partial' => 'info'
                                                ];
                                                $statusColor = $statusColors[$bill->status ?? 'pending'] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColor); ?>">
                                                <?php echo e(ucfirst($bill->status ?? 'pending')); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $bill)): ?>
                                                    <a href="<?php echo e(route('bills.show', $bill)); ?>" class="btn btn-outline-primary" title="View">
                                                        <i class="ri-eye-line"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $bill)): ?>
                                                    <a href="<?php echo e(route('bills.edit', $bill)); ?>" class="btn btn-outline-warning" title="Edit">
                                                        <i class="ri-edit-line"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if($bill->status === 'pending' || $bill->status === 'overdue'): ?>
                                                    <button type="button" class="btn btn-outline-success mark-paid" 
                                                            data-bill-id="<?php echo e($bill->id); ?>" title="Mark as Paid">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $bill)): ?>
                                                    <button type="button" class="btn btn-outline-danger delete-bill" 
                                                            data-bill-id="<?php echo e($bill->id); ?>" 
                                                            data-bill-number="<?php echo e($bill->bill_number ?? 'B-' . str_pad($bill->id, 6, '0', STR_PAD_LEFT)); ?>" 
                                                            title="Delete">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if(method_exists($bills, 'links')): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($bills->links()); ?>

                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-bill-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Bills Found</h5>
                        <p class="text-muted mb-4">
                            <?php if(request('search')): ?>
                                No bills match your search criteria.
                            <?php else: ?>
                                Start by creating your first bill to track payments and invoices.
                            <?php endif; ?>
                        </p>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Bill::class)): ?>
                            <a href="<?php echo e(route('bills.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Create First Bill
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark bill as paid
    document.querySelectorAll('.mark-paid').forEach(button => {
        button.addEventListener('click', function() {
            const billId = this.dataset.billId;

            Swal.fire({
                title: 'Mark as Paid',
                text: 'Are you sure you want to mark this bill as paid?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-check-line me-1"></i>Yes, Mark as Paid',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/bills/${billId}/mark-paid`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Delete bill
    document.querySelectorAll('.delete-bill').forEach(button => {
        button.addEventListener('click', function() {
            const billId = this.dataset.billId;
            const billNumber = this.dataset.billNumber;

            Swal.fire({
                title: 'Delete Bill',
                html: `Are you sure you want to delete:<br><br><strong>${billNumber}</strong>?<br><br><small class="text-danger">This action cannot be undone.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/bills/${billId}`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/bills/index.blade.php ENDPATH**/ ?>