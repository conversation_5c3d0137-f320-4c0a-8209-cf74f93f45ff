
    <!-- Brand container starts -->
    <div class="brand-container d-flex align-items-center justify-content-between">

    <!-- App brand starts -->
    <div class="app-brand ms-3">
        <a href="{{ route('dashboard') }}">
        <img src="{{ asset('assets/images/logo.svg') }}" class="logo" alt="Property Management System">
        </a>
    </div>
    <!-- App brand ends -->

    <!-- Pin sidebar starts -->
    <button type="button" class="pin-sidebar me-3">
        <i class="ri-menu-line"></i>
    </button>
    <!-- Pin sidebar ends -->

    </div>
    <!-- Brand container ends -->

    <!-- Sidebar profile starts -->
    <div class="sidebar-profile">
    @auth
        <div class="profile-photo-container position-relative" style="width: 80px; height: 80px; margin: 0 auto 10px;">
            <img src="{{ auth()->user()->profile_photo_url }}"
                 class="rounded-5 border border-primary border-3"
                 alt="{{ auth()->user()->name }}"
                 style="width: 100%; height: 100%; object-fit: cover;">

            <!-- Profile Photo Hover Overlay -->
            <div class="profile-photo-overlay position-absolute top-0 start-0 w-100 h-100 rounded-5 d-flex align-items-center justify-content-center"
                 style="background: rgba(0,123,255,0.8); opacity: 0; transition: opacity 0.3s; cursor: pointer;"
                 onmouseover="this.style.opacity='1'"
                 onmouseout="this.style.opacity='0'"
                 onclick="showProfilePhotoModal()">
                <i class="ri-camera-line text-white fs-4"></i>
            </div>
        </div>

        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary">{{ auth()->user()->name }}</h6>
        <small class="profile-name text-nowrap text-truncate">
            {{ ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User')) }}
        </small>
        @if(auth()->user()->hasProfilePhoto())
            <div class="mt-1">
                <small class="text-success">
                    <i class="ri-camera-line me-1"></i>Custom Photo
                </small>
            </div>
        @else
            <div class="mt-1">
                <small class="text-muted">
                    <i class="ri-user-line me-1"></i>Default Avatar
                </small>
            </div>
        @endif
    @else
        <img src="{{ asset('assets/images/doctor5.png') }}" class="rounded-5 border border-primary border-3"
            alt="Property Management System">
        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary">Guest</h6>
        <small class="profile-name text-nowrap text-truncate">Guest User</small>
    @endauth
    </div>
    <!-- Sidebar profile ends -->

    <!-- Sidebar menu starts -->
    <div class="sidebarMenuScroll">
    <ul class="sidebar-menu">
        <li class="{{ request()->routeIs('dashboard') ? 'active current-page' : '' }}">
        <a href="{{ route('dashboard') }}">
            <i class="ri-home-6-line"></i>
            <span class="menu-text">Dashboard</span>
        </a>
        </li>

        @can('view_properties')
        <li class="treeview {{ request()->routeIs('admin.properties.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-building-line"></i>
            <span class="menu-text">Properties</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">All Properties</a>
            </li>
            <li>
            <a href="#!">Add Property</a>
            </li>
            <li>
            <a href="#!">Property Reports</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('view_units')
        <li class="treeview {{ request()->routeIs('admin.units.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-home-line"></i>
            <span class="menu-text">Units</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">All Units</a>
            </li>
            <li>
            <a href="#!">Available Units</a>
            </li>
            <li>
            <a href="#!">Occupied Units</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('view_complaints')
        <li class="treeview {{ request()->routeIs('admin.complaints.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-customer-service-line"></i>
            <span class="menu-text">Complaints</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">All Complaints</a>
            </li>
            <li>
            <a href="#!">Open Complaints</a>
            </li>
            <li>
            <a href="#!">Resolved Complaints</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('view_users')
        <li class="treeview {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-group-2-line"></i>
            <span class="menu-text">Users</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('admin.users.create') }}">New User</a>
            </li>
            <li>
            <a href="{{ route('admin.users.index') }}">List Users</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('manage_roles')
        <li class="treeview {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-shield-user-line"></i>
            <span class="menu-text">Roles & Permissions</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('admin.roles.create') }}">New Role</a>
            </li>
            <li>
            <a href="{{ route('admin.roles.index') }}">List Roles</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('view_financial_reports')
        <li class="treeview {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-money-dollar-circle-line"></i>
            <span class="menu-text">Financial Reports</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">Revenue Reports</a>
            </li>
            <li>
            <a href="#!">Expense Reports</a>
            </li>
            <li>
            <a href="#!">Rent Collection</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('manage-visitors')
        <li class="treeview {{ request()->routeIs('admin.visitors.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-user-add-line"></i>
            <span class="menu-text">Visitor Management</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">Check-in Visitor</a>
            </li>
            <li>
            <a href="#!">Visitor History</a>
            </li>
            <li>
            <a href="#!">Visitor Reports</a>
            </li> 
        </ul>
        </li>
        @endcan

        @can('manage-system')
        <li class="treeview {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-settings-line"></i>
            <span class="menu-text">System Settings</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="#!">General Settings</a>
            </li>
            <li>
            <a href="#!">Email Settings</a>
            </li>
            <li>
            <a href="#!">Backup & Restore</a>
            </li>
        </ul>
        </li>
        @endcan
    </ul>
    </div>
    <!-- Sidebar menu ends -->

    <!-- Sidebar contact starts -->
    <div class="sidebar-contact">
    <p class="fw-light mb-1 text-nowrap text-truncate">Emergency Contact</p>
    <h5 class="m-0 lh-1 text-nowrap text-truncate">+60 1163844258</h5>
    <i class="ri-phone-line"></i>
    </div>
    <!-- Sidebar contact ends -->
