
    <!-- Brand container starts -->
    <div class="brand-container d-flex align-items-center justify-content-between">

    <!-- App brand starts -->
    <div class="app-brand ms-3">
        <a href="{{ route('dashboard') }}">
            <img src="{{ asset('assets/images/logo.svg') }}" class="logo" alt="Property Management System">
        </a>
    </div>
    <!-- App brand ends -->

    <!-- Pin sidebar starts -->
    <button type="button" class="pin-sidebar me-3">
        <i class="ri-menu-line"></i>
    </button>
    <!-- Pin sidebar ends -->

    </div>
    <!-- Brand container ends -->

    <!-- Sidebar profile starts -->
    <div class="sidebar-profile">
    @auth
        <div class="profile-photo-container position-relative" style="width: 80px; height: 80px; margin: 0 auto 10px;">
            <img src="{{ auth()->user()->profile_photo_url }}"
                 class="rounded-5 border border-primary border-3"
                 alt="{{ auth()->user()->name }}"
                 style="width: 100%; height: 100%; object-fit: cover;">

            <!-- Profile Photo Hover Overlay -->
            <div class="profile-photo-overlay position-absolute top-0 start-0 w-100 h-100 rounded-5 d-flex align-items-center justify-content-center"
                 style="background: rgba(0,123,255,0.8); opacity: 0; transition: opacity 0.3s; cursor: pointer;"
                 onmouseover="this.style.opacity='1'"
                 onmouseout="this.style.opacity='0'"
                 onclick="showProfilePhotoModal()">
                <i class="ri-camera-line text-white fs-4"></i>
            </div>
        </div>

        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary">{{ auth()->user()->name }}</h6>
        <small class="profile-name text-nowrap text-truncate">
            {{ ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User')) }}
        </small>
        @if(auth()->user()->hasProfilePhoto())
            <div class="mt-1">
                <small class="text-success">
                    <i class="ri-camera-line me-1"></i>Custom Photo
                </small>
            </div>
        @else
            <div class="mt-1">
                <small class="text-muted">
                    <i class="ri-user-line me-1"></i>Default Avatar
                </small>
            </div>
        @endif
    @else
        <img src="{{ asset('assets/images/doctor5.png') }}" class="rounded-5 border border-primary border-3"
            alt="Property Management System">
        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary">Guest</h6>
        <small class="profile-name text-nowrap text-truncate">Guest User</small>
    @endauth
    </div>
    <!-- Sidebar profile ends -->

    <!-- Sidebar menu starts -->
    <div class="sidebarMenuScroll">
    <ul class="sidebar-menu">
        <li class="{{ request()->routeIs('dashboard') ? 'active current-page' : '' }}">
        <a href="{{ route('dashboard') }}">
            <i class="ri-home-6-line"></i>
            <span class="menu-text">Dashboard</span>
        </a>
        </li>

        @can('view_properties')
        <li class="treeview {{ request()->routeIs('properties.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-building-line"></i>
            <span class="menu-text">Properties</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('properties.index') }}">All Properties</a>
            </li>
            @can('create', App\Models\Property::class)
            <li>
            <a href="{{ route('properties.create') }}">Add Property</a>
            </li>
            @endcan
            <li>
            <a href="{{ route('reports.property') }}">Property Reports</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('manage_own_properties')
        @if(!auth()->user()->can('view_properties'))
        <li class="treeview {{ request()->routeIs('properties.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-building-line"></i>
            <span class="menu-text">My Properties</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('properties.index') }}">All Properties</a>
            </li>
            <li>
            <a href="{{ route('properties.create') }}">Add Property</a>
            </li>
            <li>
            <a href="{{ route('reports.property') }}">Property Reports</a>
            </li>
        </ul>
        </li>
        @endif
        @endcan

        @can('view_units')
        <li class="treeview {{ request()->routeIs('units.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-home-line"></i>
            <span class="menu-text">Units</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('units.index') }}">All Units</a>
            </li>
            @can('create', App\Models\Unit::class)
            <li>
            <a href="{{ route('units.create') }}">Add Unit</a>
            </li>
            @endcan
            <li>
            <a href="{{ route('units.index', ['status' => 'available']) }}">Available Units</a>
            </li>
            <li>
            <a href="{{ route('units.index', ['status' => 'occupied']) }}">Occupied Units</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('manage_own_properties')
        @if(!auth()->user()->can('view_units'))
        <li class="treeview {{ request()->routeIs('units.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-home-line"></i>
            <span class="menu-text">My Units</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('units.index') }}">All Units</a>
            </li>
            <li>
            <a href="{{ route('units.create') }}">Add Unit</a>
            </li>
            <li>
            <a href="{{ route('units.index', ['status' => 'available']) }}">Available Units</a>
            </li>
            <li>
            <a href="{{ route('units.index', ['status' => 'occupied']) }}">Occupied Units</a>
            </li>
        </ul>
        </li>
        @endif
        @endcan

        <!-- Billing & Payments -->
        <li class="treeview {{ request()->routeIs('bills.*', 'payments.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-money-dollar-circle-line"></i>
            <span class="menu-text">Billing & Payments</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('bills.index') }}">All Bills</a>
            </li>
            <li>
            <a href="{{ route('bills.create') }}">Create Bill</a>
            </li>
            <li>
            <a href="{{ route('payments.index') }}">All Payments</a>
            </li>
            <li>
            <a href="{{ route('payments.create') }}">Record Payment</a>
            </li>
        </ul>
        </li>

        @can('view_complaints')
        <li class="treeview {{ request()->routeIs('complaints.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-customer-service-line"></i>
            <span class="menu-text">Complaints</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('complaints.index') }}">All Complaints</a>
            </li>
            <li>
            <a href="{{ route('complaints.create') }}">New Complaint</a>
            </li>
            <li>
            <a href="{{ route('complaints.index', ['status' => 'open']) }}">Open Complaints</a>
            </li>
            <li>
            <a href="{{ route('complaints.index', ['status' => 'resolved']) }}">Resolved Complaints</a>
            </li>
        </ul>
        </li>
        @endcan

        <!-- Task & Notification System -->
        <li class="treeview {{ request()->routeIs('tasks.*', 'notifications.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-task-line"></i>
            <span class="menu-text">Tasks & Notifications</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('tasks.index') }}">All Tasks</a>
            </li>
            <li>
            <a href="{{ route('tasks.create') }}">Create Task</a>
            </li>
            <li>
            <a href="{{ route('tasks.dashboard') }}">Task Dashboard</a>
            </li>
            <li>
            <a href="{{ route('notifications.index') }}">Notifications</a>
            </li>
            @can('create', App\Models\Notification::class)
            <li>
            <a href="{{ route('notifications.create') }}">Send Notification</a>
            </li>
            @endcan
        </ul>
        </li>

        <!-- Document Management -->
        <li class="treeview {{ request()->routeIs('documents.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-file-text-line"></i>
            <span class="menu-text">Documents</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('documents.index') }}">All Documents</a>
            </li>
            @can('create', App\Models\Document::class)
            <li>
            <a href="{{ route('documents.create') }}">Upload Document</a>
            </li>
            @endcan
            <li>
            <a href="{{ route('documents.expiring') }}">Expiring Documents</a>
            </li>
            <li>
            <a href="{{ route('documents.index', ['status' => 'expired']) }}">Expired Documents</a>
            </li>
        </ul>
        </li>

        @can('view_users')
        <li class="treeview {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-group-2-line"></i>
            <span class="menu-text">Users</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('admin.users.create') }}">New User</a>
            </li>
            <li>
            <a href="{{ route('admin.users.index') }}">List Users</a>
            </li>
        </ul>
        </li>
        @endcan

        @can('manage_roles')
        <li class="treeview {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-shield-user-line"></i>
            <span class="menu-text">Roles & Permissions</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('admin.roles.create') }}">New Role</a>
            </li>
            <li>
            <a href="{{ route('admin.roles.index') }}">List Roles</a>
            </li>
        </ul>
        </li>
        @endcan

        <!-- Reports Section -->
        <li class="treeview {{ request()->routeIs('reports.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-bar-chart-line"></i>
            <span class="menu-text">Reports & Analytics</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('reports.property') }}">Property Reports</a>
            </li>
            <li>
            <a href="{{ route('reports.financial') }}">Financial Reports</a>
            </li>
            <li>
            <a href="{{ route('reports.property') }}">Occupancy Analytics</a>
            </li>
            <li>
            <a href="{{ route('reports.financial') }}">Revenue Analysis</a>
            </li>
        </ul>
        </li>

        <!-- Visitor Management -->
        <li class="treeview {{ request()->routeIs('visitors.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-user-add-line"></i>
            <span class="menu-text">Visitor Management</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('visitors.create') }}">Check-in Visitor</a>
            </li>
            <li>
            <a href="{{ route('visitors.index') }}">Visitor History</a>
            </li>
            <li>
            <a href="{{ route('visitors.dashboard') }}">Visitor Dashboard</a>
            </li>
            <li>
            <a href="{{ route('visitors.index', ['status' => 'checked_in']) }}">Currently Checked In</a>
            </li>
        </ul>
        </li>

        @can('manage-system')
        <li class="treeview {{ request()->routeIs('settings.*') ? 'active' : '' }}">
        <a href="#!">
            <i class="ri-settings-line"></i>
            <span class="menu-text">System Settings</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="{{ route('settings.index') }}">General Settings</a>
            </li>
            <li>
            <a href="{{ route('settings.index') }}#email">Email Configuration</a>
            </li>
            <li>
            <a href="{{ route('settings.index') }}#backup">Backup & Restore</a>
            </li>
            <li>
            <a href="{{ route('settings.index') }}#company">Company Information</a>
            </li>
        </ul>
        </li>
        @endcan
    </ul>
    </div>
    <!-- Sidebar menu ends -->

    <!-- Sidebar contact starts -->
    <div class="sidebar-contact">
    <p class="fw-light mb-1 text-nowrap text-truncate">Emergency Contact</p>
    <h5 class="m-0 lh-1 text-nowrap text-truncate">+60 1163844258</h5>
    <i class="ri-phone-line"></i>
    </div>
    <!-- Sidebar contact ends -->
