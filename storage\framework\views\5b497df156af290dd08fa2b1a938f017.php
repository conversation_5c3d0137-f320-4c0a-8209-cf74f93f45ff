<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-home-add-line text-primary me-2"></i>Create New Unit
                        </h4>
                        <p class="text-muted mb-0">
                            Add a new unit to the property management system
                        </p>
                    </div>
                    <div>
                        <a href="<?php echo e(route('units.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Units
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('units.store')); ?>" method="POST" id="createUnitForm">
                    <?php echo csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Property Selection -->
                            <div class="mb-3">
                                <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="property_id" name="property_id" required>
                                    <option value="">Select Property</option>
                                    <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($property->id); ?>" <?php echo e(old('property_id') == $property->id ? 'selected' : ''); ?>>
                                            <?php echo e($property->name); ?> - <?php echo e($property->address); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Unit Number -->
                            <div class="mb-3">
                                <label for="unit_number" class="form-label">Unit Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['unit_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="unit_number" name="unit_number" value="<?php echo e(old('unit_number')); ?>" 
                                       placeholder="e.g., 101, A-1, 2B" required>
                                <?php $__errorArgs = ['unit_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Floor -->
                            <div class="mb-3">
                                <label for="floor" class="form-label">Floor</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['floor'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="floor" name="floor" value="<?php echo e(old('floor')); ?>" 
                                       placeholder="e.g., 1, 2, 3" min="0" max="100">
                                <?php $__errorArgs = ['floor'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Unit Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Unit Type <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="studio" <?php echo e(old('type') == 'studio' ? 'selected' : ''); ?>>Studio</option>
                                    <option value="1_bedroom" <?php echo e(old('type') == '1_bedroom' ? 'selected' : ''); ?>>1 Bedroom</option>
                                    <option value="2_bedroom" <?php echo e(old('type') == '2_bedroom' ? 'selected' : ''); ?>>2 Bedroom</option>
                                    <option value="3_bedroom" <?php echo e(old('type') == '3_bedroom' ? 'selected' : ''); ?>>3 Bedroom</option>
                                    <option value="4_bedroom" <?php echo e(old('type') == '4_bedroom' ? 'selected' : ''); ?>>4 Bedroom</option>
                                    <option value="penthouse" <?php echo e(old('type') == 'penthouse' ? 'selected' : ''); ?>>Penthouse</option>
                                    <option value="commercial" <?php echo e(old('type') == 'commercial' ? 'selected' : ''); ?>>Commercial</option>
                                    <option value="office" <?php echo e(old('type') == 'office' ? 'selected' : ''); ?>>Office</option>
                                    <option value="retail" <?php echo e(old('type') == 'retail' ? 'selected' : ''); ?>>Retail</option>
                                    <option value="other" <?php echo e(old('type') == 'other' ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Size -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="size" class="form-label">Size (sq ft)</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['size'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="size" name="size" value="<?php echo e(old('size')); ?>" 
                                               placeholder="e.g., 1200" min="1" step="0.01">
                                        <?php $__errorArgs = ['size'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bedrooms" class="form-label">Bedrooms</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['bedrooms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="bedrooms" name="bedrooms" value="<?php echo e(old('bedrooms')); ?>" 
                                               placeholder="e.g., 2" min="0" max="10">
                                        <?php $__errorArgs = ['bedrooms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Bathrooms and Parking -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bathrooms" class="form-label">Bathrooms</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['bathrooms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="bathrooms" name="bathrooms" value="<?php echo e(old('bathrooms')); ?>" 
                                               placeholder="e.g., 2" min="0" max="10" step="0.5">
                                        <?php $__errorArgs = ['bathrooms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="parking_spaces" class="form-label">Parking Spaces</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['parking_spaces'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="parking_spaces" name="parking_spaces" value="<?php echo e(old('parking_spaces')); ?>" 
                                               placeholder="e.g., 1" min="0" max="5">
                                        <?php $__errorArgs = ['parking_spaces'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="description" name="description" rows="4" 
                                          placeholder="Additional details about the unit..."><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Status -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="status" name="status" required>
                                    <option value="available" <?php echo e(old('status', 'available') == 'available' ? 'selected' : ''); ?>>Available</option>
                                    <option value="occupied" <?php echo e(old('status') == 'occupied' ? 'selected' : ''); ?>>Occupied</option>
                                    <option value="maintenance" <?php echo e(old('status') == 'maintenance' ? 'selected' : ''); ?>>Under Maintenance</option>
                                    <option value="reserved" <?php echo e(old('status') == 'reserved' ? 'selected' : ''); ?>>Reserved</option>
                                </select>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Rent Amount -->
                            <div class="mb-3">
                                <label for="rent_amount" class="form-label">Monthly Rent</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control <?php $__errorArgs = ['rent_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="rent_amount" name="rent_amount" value="<?php echo e(old('rent_amount')); ?>" 
                                           placeholder="0.00" min="0" step="0.01">
                                    <?php $__errorArgs = ['rent_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Security Deposit -->
                            <div class="mb-3">
                                <label for="security_deposit" class="form-label">Security Deposit</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control <?php $__errorArgs = ['security_deposit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="security_deposit" name="security_deposit" value="<?php echo e(old('security_deposit')); ?>" 
                                           placeholder="0.00" min="0" step="0.01">
                                    <?php $__errorArgs = ['security_deposit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Tenant Assignment -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Assign Tenant (Optional)</label>
                                <select class="form-select <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="tenant_id" name="tenant_id">
                                    <option value="">No Tenant Assigned</option>
                                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($tenant->id); ?>" <?php echo e(old('tenant_id') == $tenant->id ? 'selected' : ''); ?>>
                                            <?php echo e($tenant->name); ?> (<?php echo e($tenant->email); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Lease Dates (if tenant assigned) -->
                            <div id="lease-dates" style="display: none;">
                                <div class="mb-3">
                                    <label for="lease_start_date" class="form-label">Lease Start Date</label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['lease_start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="lease_start_date" name="lease_start_date" value="<?php echo e(old('lease_start_date')); ?>">
                                    <?php $__errorArgs = ['lease_start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="lease_end_date" class="form-label">Lease End Date</label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['lease_end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="lease_end_date" name="lease_end_date" value="<?php echo e(old('lease_end_date')); ?>">
                                    <?php $__errorArgs = ['lease_end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Features -->
                            <div class="mb-3">
                                <label class="form-label">Features</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_balcony" name="features[]" value="balcony" 
                                           <?php echo e(in_array('balcony', old('features', [])) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="has_balcony">Balcony</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_ac" name="features[]" value="air_conditioning" 
                                           <?php echo e(in_array('air_conditioning', old('features', [])) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="has_ac">Air Conditioning</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_heating" name="features[]" value="heating" 
                                           <?php echo e(in_array('heating', old('features', [])) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="has_heating">Heating</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="furnished" name="features[]" value="furnished" 
                                           <?php echo e(in_array('furnished', old('features', [])) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="furnished">Furnished</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="pet_friendly" name="features[]" value="pet_friendly" 
                                           <?php echo e(in_array('pet_friendly', old('features', [])) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="pet_friendly">Pet Friendly</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?php echo e(route('units.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create Unit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide lease dates based on tenant selection
    const tenantSelect = document.getElementById('tenant_id');
    const leaseDates = document.getElementById('lease-dates');
    const statusSelect = document.getElementById('status');

    function toggleLeaseDates() {
        if (tenantSelect.value) {
            leaseDates.style.display = 'block';
            statusSelect.value = 'occupied';
        } else {
            leaseDates.style.display = 'none';
            statusSelect.value = 'available';
        }
    }

    tenantSelect.addEventListener('change', toggleLeaseDates);
    
    // Initialize on page load
    toggleLeaseDates();

    // Form validation
    const form = document.getElementById('createUnitForm');
    form.addEventListener('submit', function(e) {
        const propertyId = document.getElementById('property_id').value;
        const unitNumber = document.getElementById('unit_number').value;
        const tenantId = document.getElementById('tenant_id').value;
        const leaseStartDate = document.getElementById('lease_start_date').value;
        const leaseEndDate = document.getElementById('lease_end_date').value;

        // Validate required fields
        if (!propertyId || !unitNumber) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Validate lease dates if tenant is assigned
        if (tenantId && (!leaseStartDate || !leaseEndDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Lease Dates',
                text: 'Please provide lease start and end dates when assigning a tenant.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Validate lease date logic
        if (leaseStartDate && leaseEndDate && new Date(leaseStartDate) >= new Date(leaseEndDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Lease Dates',
                text: 'Lease end date must be after the start date.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });

    // Auto-suggest unit number based on property and floor
    const floorInput = document.getElementById('floor');
    const unitNumberInput = document.getElementById('unit_number');

    floorInput.addEventListener('change', function() {
        const floor = this.value;
        if (floor && !unitNumberInput.value) {
            // Suggest unit number format: floor + 01, 02, etc.
            const suggestedNumber = floor.padStart(1, '0') + '01';
            unitNumberInput.placeholder = `Suggested: ${suggestedNumber}`;
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/units/create.blade.php ENDPATH**/ ?>