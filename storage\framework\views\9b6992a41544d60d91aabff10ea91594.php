
    <!-- Brand container sm starts -->
    <div class="brand-container-sm d-xl-none d-flex align-items-center">

        <!-- App brand starts -->
        <div class="app-brand">
        <a href="<?php echo e(route('dashboard')); ?>">
            <img src="<?php echo e(asset('assets/images/logo.svg')); ?>" class="logo" alt="Property Management System">
        </a>
        </div>
        <!-- App brand ends -->

        <!-- Toggle sidebar starts -->
        <button type="button" class="toggle-sidebar">
        <i class="ri-menu-line"></i>
        </button>
        <!-- Toggle sidebar ends -->

    </div>
    <!-- Brand container sm ends -->

    <!-- Search container starts -->
    <div class="search-container d-xl-block d-none">
        <input type="text" class="form-control" id="searchId" placeholder="Search">
        <i class="ri-search-line"></i>
    </div>
    <!-- Search container ends -->

    <!-- App header actions starts -->
    <div class="header-actions">

        <!-- Header actions starts -->
        <div class="d-lg-flex d-none gap-2">

        <!-- Language selector dropdown starts -->
        <div class="dropdown">
            <a class="dropdown-toggle header-icon d-flex align-items-center text-white text-decoration-none" href="#!" role="button" data-bs-toggle="dropdown"
            aria-expanded="false">
            <i class="ri-global-line fs-5 me-2 text-white"></i>
            <span class="d-none d-md-inline text-white"><?php echo e(session('locale', 'en') == 'en' ? 'English' : 'Bahasa'); ?></span>
            </a>
            <div class="dropdown-menu dropdown-menu-end">
                <h6 class="dropdown-header">
                    <i class="ri-translate-2 me-1"></i>Select Language
                </h6>
                <a href="<?php echo e(route('language.switch', 'en')); ?>" class="dropdown-item <?php echo e(session('locale', 'en') == 'en' ? 'active' : ''); ?>">
                    <img src="<?php echo e(asset('assets/images/flags/1x1/us.svg')); ?>" class="me-2" style="width: 20px; height: 15px;" alt="">
                    English
                    <?php if(session('locale', 'en') == 'en'): ?>
                        <i class="ri-check-line ms-auto text-success"></i>
                    <?php endif; ?>
                </a>
                <a href="<?php echo e(route('language.switch', 'id')); ?>" class="dropdown-item <?php echo e(session('locale') == 'id' ? 'active' : ''); ?>">
                    <img src="<?php echo e(asset('assets/images/flags/1x1/id.svg')); ?>" class="me-2" style="width: 20px; height: 15px;" alt="">
                    Bahasa
                    <?php if(session('locale') == 'id'): ?>
                        <i class="ri-check-line ms-auto text-success"></i>
                    <?php endif; ?>
                </a>
            </div>
        </div>
        <!-- Language selector dropdown ends -->

        <!-- Notifications dropdown starts -->
        <?php if(auth()->guard()->check()): ?>
        <div class="dropdown">
            <a class="dropdown-toggle header-icon d-flex align-items-center text-white text-decoration-none position-relative"
               href="#!" role="button" data-bs-toggle="dropdown" aria-expanded="false" id="notificationDropdown">
                <i class="ri-notification-line fs-5 text-white"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                      id="notificationCount" style="display: none; font-size: 10px; min-width: 18px; height: 18px;">
                    0
                </span>
            </a>
            <div class="dropdown-menu dropdown-menu-end notification-dropdown shadow-lg"
                 aria-labelledby="notificationDropdown" style="width: 380px; max-height: 450px;">

                <!-- Dropdown Header -->
                <div class="dropdown-header d-flex justify-content-between align-items-center py-3 px-3 border-bottom">
                    <h6 class="mb-0 fw-semibold">
                        <i class="ri-notification-line me-2 text-primary"></i>Notifications
                    </h6>
                    <div class="d-flex gap-1">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="markAllReadBtn" title="Mark all as read">
                            <i class="ri-check-double-line"></i>
                        </button>
                        <a href="<?php echo e(route('notifications.index')); ?>" class="btn btn-sm btn-outline-secondary" title="View all">
                            <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>

                <!-- Notification List -->
                <div id="notificationList" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted mb-0 mt-2">Loading notifications...</p>
                    </div>
                </div>

                <!-- Dropdown Footer -->
                <div class="dropdown-footer border-top">
                    <a href="<?php echo e(route('notifications.index')); ?>" class="btn btn-primary btn-sm w-100 m-2">
                        <i class="ri-eye-line me-1"></i>View All Notifications
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <!-- Notifications dropdown ends -->

        </div>
        <!-- Header actions ends -->

        <!-- Header user settings starts -->
        <?php if(auth()->guard()->check()): ?>
        <div class="dropdown ms-5">
            <a id="userSettings" class="dropdown-toggle d-flex align-items-center text-decoration-none" href="#!" role="button"
                data-bs-toggle="dropdown" aria-expanded="false">
                <div class="avatar-box me-3">
                    <img src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                         class="rounded-circle border border-2 border-white shadow-sm"
                         alt="<?php echo e(auth()->user()->name); ?>"
                         style="width: 42px; height: 42px; object-fit: cover;">
                    <span class="status <?php echo e(auth()->user()->is_active ? 'online' : 'busy'); ?>"></span>
                </div>
                <div class="d-none d-lg-block">
                    <div class="fw-semibold text-white mb-0" style="font-size: 14px; line-height: 1.2;">
                        <?php echo e(explode(' ', auth()->user()->name)[0]); ?>

                    </div>
                    <small class="text-white-50" style="font-size: 12px; line-height: 1;">
                        <?php echo e(ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User'))); ?>

                    </small>
                </div>
            </a>
            <div class="dropdown-menu dropdown-menu-end dropdown-300 shadow-lg">
                <!-- User Info Header -->
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="avatar-box me-3">
                        <img src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                             class="rounded-circle border border-2 border-primary"
                             alt="<?php echo e(auth()->user()->name); ?>"
                             style="width: 50px; height: 50px; object-fit: cover;">
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-semibold"><?php echo e(auth()->user()->name); ?></h6>
                        <small class="text-muted"><?php echo e(auth()->user()->email); ?></small>
                        <div class="mt-1">
                            <span class="badge bg-<?php echo e(auth()->user()->is_active ? 'success' : 'secondary'); ?> badge-sm">
                                <?php echo e(auth()->user()->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                            <span class="badge bg-primary badge-sm">
                                <?php echo e(ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User'))); ?>

                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Actions -->
                <div class="p-2">
                    <a href="<?php echo e(route('profile.show')); ?>" class="dropdown-item rounded">
                        <i class="ri-user-line me-2 text-primary"></i>
                        View Profile
                    </a>
                    <a href="<?php echo e(route('profile.edit')); ?>" class="dropdown-item rounded">
                        <i class="ri-edit-line me-2 text-warning"></i>
                        Edit Profile
                    </a>
                    <a href="#!" onclick="showProfilePhotoModal()" class="dropdown-item rounded">
                        <i class="ri-camera-line me-2 text-info"></i>
                        Update Photo
                    </a>
                    <div class="dropdown-divider my-2"></div>
                    <a href="<?php echo e(route('settings')); ?>" class="dropdown-item rounded">
                        <i class="ri-settings-3-line me-2 text-secondary"></i>
                        Settings
                    </a>
                    <div class="dropdown-divider my-2"></div>
                </div>

                <!-- Logout Button -->
                <div class="p-3 pt-0">
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-grid" id="logoutForm">
                        <?php echo csrf_field(); ?>
                        <button type="button" class="btn btn-danger btn-sm" onclick="confirmLogout()">
                            <i class="ri-logout-circle-line me-1"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="ms-3">
            <a href="<?php echo e(route('login')); ?>" class="btn btn-primary btn-sm">
                <i class="ri-login-circle-line me-1"></i>
                Login
            </a>
        </div>
        <?php endif; ?>
        <!-- Header user settings ends -->

    </div>
    <!-- App header actions ends --><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/partials/header.blade.php ENDPATH**/ ?>