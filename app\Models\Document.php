<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'property_id',
        'unit_id',
        'tenant_id',
        'uploaded_by',
        'issue_date',
        'expiry_date',
        'is_expired',
        'expiry_alert_sent',
        'alert_days_before',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'expiry_date' => 'date',
        'is_expired' => 'boolean',
        'expiry_alert_sent' => 'boolean',
        'metadata' => 'array',
    ];

    // Relationships
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    // Scopes
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->whereBetween('expiry_date', [now(), now()->addDays($days)]);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByProperty($query, $propertyId)
    {
        return $query->where('property_id', $propertyId);
    }

    public function scopeByUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    public function scopeByTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeWithExpiry($query)
    {
        return $query->whereNotNull('expiry_date');
    }

    public function scopeNeedsAlert($query)
    {
        return $query->whereNotNull('expiry_date')
            ->where('expiry_alert_sent', false)
            ->where('expiry_date', '<=', now()->addDays(30));
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date &&
               $this->expiry_date->isFuture() &&
               $this->expiry_date->diffInDays() <= $this->alert_days_before;
    }

    public function getDaysUntilExpiryAttribute(): ?int
    {
        return $this->expiry_date ? $this->expiry_date->diffInDays(now(), false) : null;
    }

    public function getFileSizeFormattedAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getDownloadUrlAttribute(): string
    {
        return route('documents.download', $this);
    }

    public function getViewUrlAttribute(): string
    {
        return route('documents.view', $this);
    }

    // Methods
    public function markAsExpired(): bool
    {
        return $this->update(['is_expired' => true]);
    }

    public function markExpiryAlertSent(): bool
    {
        return $this->update(['expiry_alert_sent' => true]);
    }

    public function deleteFile(): bool
    {
        if (Storage::disk('public')->exists($this->file_path)) {
            return Storage::disk('public')->delete($this->file_path);
        }
        return true;
    }

    public function getFileUrl(): string
    {
        return Storage::disk('public')->url($this->file_path);
    }

    public function checkExpiry(): bool
    {
        if ($this->expiry_date && $this->expiry_date->isPast() && !$this->is_expired) {
            $this->markAsExpired();
            return true;
        }
        return false;
    }

    public function shouldSendExpiryAlert(): bool
    {
        return $this->expiry_date &&
               !$this->expiry_alert_sent &&
               $this->expiry_date->diffInDays() <= $this->alert_days_before;
    }

    public static function getExpiringDocuments($days = 30)
    {
        return self::whereNotNull('expiry_date')
            ->where('expiry_date', '<=', now()->addDays($days))
            ->where('expiry_date', '>=', now())
            ->where('expiry_alert_sent', false)
            ->get();
    }

    public static function getExpiredDocuments()
    {
        return self::whereNotNull('expiry_date')
            ->where('expiry_date', '<', now())
            ->where('is_expired', false)
            ->get();
    }
}
