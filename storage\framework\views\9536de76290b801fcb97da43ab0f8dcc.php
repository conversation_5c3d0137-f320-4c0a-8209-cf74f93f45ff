<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-lg-6 col-md-8 col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-body text-center p-5">
                    <!-- 404 Icon -->
                    <div class="mb-4">
                        <i class="ri-error-warning-line" style="font-size: 120px; color: #6c757d;"></i>
                    </div>
                    
                    <!-- Error Title -->
                    <h1 class="display-4 fw-bold text-primary mb-3">404</h1>
                    <h3 class="mb-3 text-dark">Page Not Found</h3>
                    
                    <!-- Error Message -->
                    <div class="mb-4">
                        <p class="text-muted mb-3">
                            Oops! The page you're looking for doesn't exist or has been moved.
                        </p>
                        <div class="alert alert-info border-0 bg-light">
                            <div class="d-flex align-items-center">
                                <i class="ri-information-line fs-4 text-info me-3"></i>
                                <div class="text-start">
                                    <strong>Don't worry!</strong> You're still logged in as <strong><?php echo e($user->name); ?></strong>.
                                    <br><small class="text-muted">You can navigate back to your dashboard or use the menu.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- User Info -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="avatar-sm me-3">
                                <img src="<?php echo e($user->profile_photo_url); ?>" 
                                     alt="<?php echo e($user->name); ?>" 
                                     class="rounded-circle border border-2 border-primary" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            </div>
                            <div class="text-start">
                                <h6 class="mb-0"><?php echo e($user->name); ?></h6>
                                <small class="text-muted">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $user->getRoleNames()->first() ?? 'User'))); ?>

                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-primary">
                            <i class="ri-dashboard-line me-2"></i>Go to Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-2"></i>Go Back
                        </button>
                        <a href="javascript:void(0)" onclick="window.location.reload()" class="btn btn-outline-info">
                            <i class="ri-refresh-line me-2"></i>Refresh Page
                        </a>
                    </div>
                    
                    <!-- Additional Help -->
                    <div class="mt-4 pt-4 border-top">
                        <h6 class="text-muted mb-3">Need Help?</h6>
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="p-3 bg-light rounded">
                                    <i class="ri-home-line fs-4 text-primary mb-2"></i>
                                    <h6 class="mb-1">Dashboard</h6>
                                    <small class="text-muted">Return to your main dashboard</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3 bg-light rounded">
                                    <i class="ri-menu-line fs-4 text-success mb-2"></i>
                                    <h6 class="mb-1">Navigation</h6>
                                    <small class="text-muted">Use the sidebar menu to navigate</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3 bg-light rounded">
                                    <i class="ri-search-line fs-4 text-warning mb-2"></i>
                                    <h6 class="mb-1">Search</h6>
                                    <small class="text-muted">Use the search feature to find content</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer Info -->
                    <div class="mt-4 pt-3 border-top">
                        <small class="text-muted">
                            <i class="ri-shield-check-line me-1"></i>
                            Your session is secure and active. 
                            <span class="text-success">Last activity: <?php echo e(now()->format('M d, Y \a\t H:i')); ?></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the dashboard button for better UX
    const dashboardBtn = document.querySelector('a[href="<?php echo e(route('dashboard')); ?>"]');
    if (dashboardBtn) {
        dashboardBtn.focus();
    }
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Press 'D' to go to dashboard
        if (e.key.toLowerCase() === 'd' && !e.ctrlKey && !e.altKey) {
            window.location.href = '<?php echo e(route('dashboard')); ?>';
        }
        
        // Press 'B' to go back
        if (e.key.toLowerCase() === 'b' && !e.ctrlKey && !e.altKey) {
            history.back();
        }
        
        // Press 'R' to refresh
        if (e.key.toLowerCase() === 'r' && !e.ctrlKey && !e.altKey) {
            window.location.reload();
        }
    });
    
    // Show a toast notification
    setTimeout(() => {
        Swal.fire({
            toast: true,
            position: 'top-end',
            icon: 'info',
            title: 'Page not found, but you\'re still logged in!',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true
        });
    }, 500);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/errors/404.blade.php ENDPATH**/ ?>