<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\Property;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Unit::with(['property', 'tenant']);
        } else {
            // Property owners see units in their properties
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Unit::whereIn('property_id', $propertyIds)->with(['property', 'tenant']);
        }

        // Apply filters
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('unit_type')) {
            $query->where('unit_type', $request->unit_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unit_number', 'like', "%{$search}%")
                  ->orWhereHas('tenant', function ($tq) use ($search) {
                      $tq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $units = $query->paginate(12);

        // Get filter options
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } else {
            $properties = $user->ownedProperties;
        }

        $unitTypes = Unit::distinct()->pluck('unit_type');

        return view('units.index', compact('units', 'properties', 'unitTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Unit::class);

        $user = Auth::user();

        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } else {
            $properties = $user->ownedProperties;
        }

        // Get tenants (users with tenant role)
        $tenants = User::whereHas('roles', function ($q) {
            $q->where('name', 'tenant');
        })->where('is_active', true)->get();

        $selectedProperty = $request->property_id ? Property::find($request->property_id) : null;

        return view('units.create', compact('properties', 'tenants', 'selectedProperty'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Unit::class);

        $validated = $request->validate([
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'required|string|max:50',
            'unit_type' => 'required|string|max:50',
            'area' => 'nullable|numeric|min:0',
            'bedrooms' => 'required|integer|min:0',
            'bathrooms' => 'required|integer|min:1',
            'rent_amount' => 'required|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'status' => 'required|in:available,occupied,maintenance,reserved',
            'tenant_id' => 'nullable|exists:users,id',
            'lease_start' => 'nullable|date',
            'lease_end' => 'nullable|date|after:lease_start',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'lease_document' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'tenant_id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
        ]);

        // Check if unit number is unique within the property
        $existingUnit = Unit::where('property_id', $validated['property_id'])
            ->where('unit_number', $validated['unit_number'])
            ->first();

        if ($existingUnit) {
            return back()->withErrors(['unit_number' => 'Unit number already exists in this property.'])->withInput();
        }

        // Handle file uploads
        if ($request->hasFile('lease_document')) {
            $validated['lease_document'] = $request->file('lease_document')->store('lease_documents', 'public');
        }

        if ($request->hasFile('tenant_id_document')) {
            $validated['tenant_id_document'] = $request->file('tenant_id_document')->store('tenant_documents', 'public');
        }

        // If tenant is assigned, set status to occupied
        if ($validated['tenant_id']) {
            $validated['status'] = 'occupied';
        }

        $unit = Unit::create($validated);

        return redirect()->route('units.show', $unit)
            ->with('success', 'Unit created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Unit $unit)
    {
        $this->authorize('view', $unit);

        $unit->load(['property', 'tenant', 'complaints.assignedTo']);

        // Get unit statistics
        $stats = [
            'total_complaints' => $unit->complaints->count(),
            'open_complaints' => $unit->complaints->where('status', '!=', 'resolved')->count(),
            'maintenance_requests' => $unit->complaints->where('category', 'maintenance')->count(),
        ];

        // Get recent complaints
        $recentComplaints = $unit->complaints()->with(['complainant', 'assignedTo'])
            ->latest()
            ->take(5)
            ->get();

        return view('units.show', compact('unit', 'stats', 'recentComplaints'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Unit $unit)
    {
        $this->authorize('update', $unit);

        $user = Auth::user();

        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } else {
            $properties = $user->ownedProperties;
        }

        // Get tenants (users with tenant role)
        $tenants = User::whereHas('roles', function ($q) {
            $q->where('name', 'tenant');
        })->where('is_active', true)->get();

        return view('units.edit', compact('unit', 'properties', 'tenants'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Unit $unit)
    {
        $this->authorize('update', $unit);

        $validated = $request->validate([
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'required|string|max:50',
            'unit_type' => 'required|string|max:50',
            'area' => 'nullable|numeric|min:0',
            'bedrooms' => 'required|integer|min:0',
            'bathrooms' => 'required|integer|min:1',
            'rent_amount' => 'required|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'status' => 'required|in:available,occupied,maintenance,reserved',
            'tenant_id' => 'nullable|exists:users,id',
            'lease_start' => 'nullable|date',
            'lease_end' => 'nullable|date|after:lease_start',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'lease_document' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'tenant_id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
        ]);

        // Check if unit number is unique within the property (excluding current unit)
        $existingUnit = Unit::where('property_id', $validated['property_id'])
            ->where('unit_number', $validated['unit_number'])
            ->where('id', '!=', $unit->id)
            ->first();

        if ($existingUnit) {
            return back()->withErrors(['unit_number' => 'Unit number already exists in this property.'])->withInput();
        }

        // Handle file uploads
        if ($request->hasFile('lease_document')) {
            // Delete old file if exists
            if ($unit->lease_document) {
                Storage::disk('public')->delete($unit->lease_document);
            }
            $validated['lease_document'] = $request->file('lease_document')->store('lease_documents', 'public');
        }

        if ($request->hasFile('tenant_id_document')) {
            // Delete old file if exists
            if ($unit->tenant_id_document) {
                Storage::disk('public')->delete($unit->tenant_id_document);
            }
            $validated['tenant_id_document'] = $request->file('tenant_id_document')->store('tenant_documents', 'public');
        }

        // If tenant is assigned, set status to occupied
        if ($validated['tenant_id']) {
            $validated['status'] = 'occupied';
        } elseif ($unit->tenant_id && !$validated['tenant_id']) {
            // If tenant is removed, set status to available
            $validated['status'] = 'available';
        }

        $unit->update($validated);

        return redirect()->route('units.show', $unit)
            ->with('success', 'Unit updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Unit $unit)
    {
        $this->authorize('delete', $unit);

        // Delete associated files
        if ($unit->lease_document) {
            Storage::disk('public')->delete($unit->lease_document);
        }
        if ($unit->tenant_id_document) {
            Storage::disk('public')->delete($unit->tenant_id_document);
        }

        $unit->delete();

        return redirect()->route('units.index')
            ->with('success', 'Unit deleted successfully!');
    }

    /**
     * Assign tenant to unit
     */
    public function assignTenant(Request $request, Unit $unit)
    {
        $this->authorize('manageOccupancy', $unit);

        $validated = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'lease_start' => 'required|date',
            'lease_end' => 'required|date|after:lease_start',
            'security_deposit' => 'nullable|numeric|min:0',
            'lease_document' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'tenant_id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
        ]);

        // Handle file uploads
        if ($request->hasFile('lease_document')) {
            $validated['lease_document'] = $request->file('lease_document')->store('lease_documents', 'public');
        }

        if ($request->hasFile('tenant_id_document')) {
            $validated['tenant_id_document'] = $request->file('tenant_id_document')->store('tenant_documents', 'public');
        }

        $validated['status'] = 'occupied';

        $unit->update($validated);

        return redirect()->route('units.show', $unit)
            ->with('success', 'Tenant assigned successfully!');
    }

    /**
     * Remove tenant from unit
     */
    public function removeTenant(Unit $unit)
    {
        $this->authorize('manageOccupancy', $unit);

        $unit->update([
            'tenant_id' => null,
            'lease_start' => null,
            'lease_end' => null,
            'status' => 'available',
        ]);

        return redirect()->route('units.show', $unit)
            ->with('success', 'Tenant removed successfully!');
    }
}
