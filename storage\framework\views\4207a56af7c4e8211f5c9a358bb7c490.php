<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-money-dollar-circle-line text-primary me-2"></i>Payments Management
                        </h4>
                        <p class="text-muted mb-0">
                            Track and manage all payments received from tenants
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('payments.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Record Payment
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index')); ?>">All Payments</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['status' => 'completed'])); ?>">Completed</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['status' => 'pending'])); ?>">Pending</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['status' => 'failed'])); ?>">Failed</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['method' => 'cash'])); ?>">Cash</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['method' => 'bank_transfer'])); ?>">Bank Transfer</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('payments.index', ['method' => 'check'])); ?>">Check</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format($stats['total_amount'] ?? 0, 2)); ?></h3>
                                <p class="mb-0">Total Received</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-money-dollar-circle-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['total_count'] ?? 0); ?></h3>
                                <p class="mb-0">Total Payments</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-file-list-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format($stats['this_month'] ?? 0, 2)); ?></h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['pending_count'] ?? 0); ?></h3>
                                <p class="mb-0">Pending</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Payments List</h5>
                    <div class="d-flex gap-2">
                        <form method="GET" action="<?php echo e(route('payments.index')); ?>" class="d-flex gap-2">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search payments..." value="<?php echo e(request('search')); ?>">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if(isset($payments) && $payments->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Payment #</th>
                                    <th>Tenant</th>
                                    <th>Property/Unit</th>
                                    <th>Bill</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong>#<?php echo e($payment->payment_number ?? 'P-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT)); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($payment->tenant): ?>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($payment->tenant->name); ?></h6>
                                                    <small class="text-muted"><?php echo e($payment->tenant->email); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">No tenant</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($payment->unit): ?>
                                                <div>
                                                    <strong><?php echo e($payment->unit->property->name ?? 'Unknown Property'); ?></strong>
                                                    <br><small class="text-muted">Unit <?php echo e($payment->unit->unit_number); ?></small>
                                                </div>
                                            <?php elseif($payment->property): ?>
                                                <strong><?php echo e($payment->property->name); ?></strong>
                                            <?php else: ?>
                                                <span class="text-muted">No property</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($payment->bill): ?>
                                                <a href="<?php echo e(route('bills.show', $payment->bill)); ?>" class="text-primary">
                                                    #<?php echo e($payment->bill->bill_number ?? 'B-' . str_pad($payment->bill->id, 6, '0', STR_PAD_LEFT)); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">No bill</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong>$<?php echo e(number_format($payment->amount ?? 0, 2)); ?></strong>
                                        </td>
                                        <td>
                                            <?php
                                                $methodColors = [
                                                    'cash' => 'success',
                                                    'bank_transfer' => 'primary',
                                                    'check' => 'info',
                                                    'credit_card' => 'warning',
                                                    'online' => 'secondary'
                                                ];
                                                $methodColor = $methodColors[$payment->payment_method ?? 'secondary'] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo e($methodColor); ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method ?? 'unknown'))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($payment->payment_date): ?>
                                                <?php echo e($payment->payment_date->format('M d, Y')); ?>

                                                <br><small class="text-muted"><?php echo e($payment->payment_date->format('H:i')); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">No date</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'completed' => 'success',
                                                    'pending' => 'warning',
                                                    'failed' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $statusColor = $statusColors[$payment->status ?? 'pending'] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColor); ?>">
                                                <?php echo e(ucfirst($payment->status ?? 'pending')); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('payments.show', $payment)); ?>" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="<?php echo e(route('payments.edit', $payment)); ?>" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                <?php if($payment->status === 'pending'): ?>
                                                    <button type="button" class="btn btn-outline-success confirm-payment" 
                                                            data-payment-id="<?php echo e($payment->id); ?>" title="Confirm Payment">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if(method_exists($payments, 'links')): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($payments->links()); ?>

                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-money-dollar-circle-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Payments Found</h5>
                        <p class="text-muted mb-4">
                            Start by recording your first payment to track tenant payments.
                        </p>
                        <a href="<?php echo e(route('payments.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Record First Payment
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Confirm payment
    document.querySelectorAll('.confirm-payment').forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.dataset.paymentId;

            Swal.fire({
                title: 'Confirm Payment',
                text: 'Are you sure you want to confirm this payment?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Confirm',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/payments/${paymentId}/confirm`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/payments/index.blade.php ENDPATH**/ ?>