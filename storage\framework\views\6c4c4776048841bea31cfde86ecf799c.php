<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-notification-line text-primary me-2"></i>Notifications
                        </h4>
                        <p class="text-muted mb-0">
                            View and manage your notifications and alerts
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Notification::class)): ?>
                            <a href="<?php echo e(route('notifications.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Send Notification
                            </a>
                        <?php endif; ?>
                        <button type="button" class="btn btn-outline-secondary" id="markAllRead">
                            <i class="ri-check-double-line me-1"></i>Mark All Read
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-notification-line fs-1 text-primary mb-2"></i>
                        <h4 class="mb-1"><?php echo e($stats['total']); ?></h4>
                        <p class="text-muted mb-0">Total Notifications</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-notification-badge-line fs-1 text-warning mb-2"></i>
                        <h4 class="mb-1"><?php echo e($stats['unread']); ?></h4>
                        <p class="text-muted mb-0">Unread</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-check-double-line fs-1 text-success mb-2"></i>
                        <h4 class="mb-1"><?php echo e($stats['read']); ?></h4>
                        <p class="text-muted mb-0">Read</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-time-line fs-1 text-info mb-2"></i>
                        <h4 class="mb-1"><?php echo e($stats['recent']); ?></h4>
                        <p class="text-muted mb-0">Recent (7 days)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('notifications.index')); ?>" class="row g-3">
                    <div class="col-md-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="task_reminder" <?php echo e(request('type') == 'task_reminder' ? 'selected' : ''); ?>>Task Reminder</option>
                            <option value="lease_expiry" <?php echo e(request('type') == 'lease_expiry' ? 'selected' : ''); ?>>Lease Expiry</option>
                            <option value="document_expiry" <?php echo e(request('type') == 'document_expiry' ? 'selected' : ''); ?>>Document Expiry</option>
                            <option value="payment_due" <?php echo e(request('type') == 'payment_due' ? 'selected' : ''); ?>>Payment Due</option>
                            <option value="maintenance_request" <?php echo e(request('type') == 'maintenance_request' ? 'selected' : ''); ?>>Maintenance Request</option>
                            <option value="system_alert" <?php echo e(request('type') == 'system_alert' ? 'selected' : ''); ?>>System Alert</option>
                            <option value="general" <?php echo e(request('type') == 'general' ? 'selected' : ''); ?>>General</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <option value="low" <?php echo e(request('priority') == 'low' ? 'selected' : ''); ?>>Low</option>
                            <option value="medium" <?php echo e(request('priority') == 'medium' ? 'selected' : ''); ?>>Medium</option>
                            <option value="high" <?php echo e(request('priority') == 'high' ? 'selected' : ''); ?>>High</option>
                            <option value="urgent" <?php echo e(request('priority') == 'urgent' ? 'selected' : ''); ?>>Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="unread" <?php echo e(request('status') == 'unread' ? 'selected' : ''); ?>>Unread</option>
                            <option value="read" <?php echo e(request('status') == 'read' ? 'selected' : ''); ?>>Read</option>
                        </select>
                    </div>
                    <div class="col-md-5 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('notifications.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications List -->
        <?php if($notifications->count() > 0): ?>
            <div class="card">
                <div class="card-body p-0">
                    <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="notification-item border-bottom p-3 <?php echo e(!$notification->is_read ? 'bg-light' : ''); ?>" 
                             data-notification-id="<?php echo e($notification->id); ?>">
                            <div class="d-flex align-items-start">
                                <!-- Notification Icon -->
                                <div class="me-3">
                                    <?php switch($notification->type):
                                        case ('task_reminder'): ?>
                                            <div class="avatar avatar-sm bg-primary">
                                                <i class="ri-task-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php case ('lease_expiry'): ?>
                                            <div class="avatar avatar-sm bg-warning">
                                                <i class="ri-calendar-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php case ('document_expiry'): ?>
                                            <div class="avatar avatar-sm bg-danger">
                                                <i class="ri-file-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php case ('payment_due'): ?>
                                            <div class="avatar avatar-sm bg-success">
                                                <i class="ri-money-dollar-circle-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php case ('maintenance_request'): ?>
                                            <div class="avatar avatar-sm bg-info">
                                                <i class="ri-tools-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php case ('system_alert'): ?>
                                            <div class="avatar avatar-sm bg-secondary">
                                                <i class="ri-alert-line text-white"></i>
                                            </div>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <div class="avatar avatar-sm bg-primary">
                                                <i class="ri-notification-line text-white"></i>
                                            </div>
                                    <?php endswitch; ?>
                                </div>

                                <!-- Notification Content -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <h6 class="mb-0 <?php echo e(!$notification->is_read ? 'fw-bold' : ''); ?>">
                                            <?php echo e($notification->title); ?>

                                        </h6>
                                        <div class="d-flex align-items-center gap-2">
                                            <!-- Priority Badge -->
                                            <span class="badge bg-<?php echo e($notification->priority == 'urgent' ? 'danger' : ($notification->priority == 'high' ? 'warning' : ($notification->priority == 'medium' ? 'info' : 'secondary'))); ?>">
                                                <?php echo e(ucfirst($notification->priority)); ?>

                                            </span>
                                            <!-- Unread Indicator -->
                                            <?php if(!$notification->is_read): ?>
                                                <span class="badge bg-primary">New</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <p class="text-muted mb-2"><?php echo e($notification->message); ?></p>
                                    
                                    <!-- Related Information -->
                                    <?php if($notification->relatedProperty || $notification->relatedUnit || $notification->relatedTask): ?>
                                        <div class="mb-2">
                                            <?php if($notification->relatedProperty): ?>
                                                <small class="text-info me-2">
                                                    <i class="ri-building-line me-1"></i><?php echo e($notification->relatedProperty->name); ?>

                                                </small>
                                            <?php endif; ?>
                                            <?php if($notification->relatedUnit): ?>
                                                <small class="text-info me-2">
                                                    <i class="ri-home-line me-1"></i>Unit <?php echo e($notification->relatedUnit->unit_number); ?>

                                                </small>
                                            <?php endif; ?>
                                            <?php if($notification->relatedTask): ?>
                                                <small class="text-info me-2">
                                                    <i class="ri-task-line me-1"></i><?php echo e($notification->relatedTask->title); ?>

                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Timestamp and Sender -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="ri-time-line me-1"></i><?php echo e($notification->time_ago); ?>

                                            <?php if($notification->createdBy): ?>
                                                • from <?php echo e($notification->createdBy->name); ?>

                                            <?php endif; ?>
                                        </small>
                                        
                                        <!-- Actions -->
                                        <div class="d-flex gap-1">
                                            <a href="<?php echo e(route('notifications.show', $notification)); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <?php if(!$notification->is_read): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success mark-read" 
                                                        data-notification-id="<?php echo e($notification->id); ?>">
                                                    <i class="ri-check-line"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $notification)): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-notification" 
                                                        data-notification-id="<?php echo e($notification->id); ?>">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <!-- Pagination -->
                    <div class="p-3">
                        <?php echo e($notifications->links()); ?>

                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-notification-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Notifications Found</h5>
                    <p class="text-muted mb-4">
                        <?php if(request()->hasAny(['type', 'priority', 'status'])): ?>
                            No notifications match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            You don't have any notifications yet. They will appear here when you receive them.
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark single notification as read
    document.querySelectorAll('.mark-read').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/notifications/${notificationId}/read`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    });

    // Mark all notifications as read
    document.getElementById('markAllRead').addEventListener('click', function() {
        fetch('/notifications/mark-all-read', {
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    });

    // Delete notification
    document.querySelectorAll('.delete-notification').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;

            Swal.fire({
                title: 'Delete Notification',
                text: 'Are you sure you want to delete this notification?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/notifications/${notificationId}`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/notifications/index.blade.php ENDPATH**/ ?>