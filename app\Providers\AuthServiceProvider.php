<?php

namespace App\Providers;

use App\Models\Property;
use App\Models\Unit;
use App\Models\Complaint;
use App\Policies\PropertyPolicy;
use App\Policies\UnitPolicy;
use App\Policies\ComplaintPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Property::class => PropertyPolicy::class,
        Unit::class => UnitPolicy::class,
        Complaint::class => ComplaintPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define additional gates if needed
        Gate::define('manage-system', function ($user) {
            return $user->hasRole('admin');
        });

        Gate::define('access-admin-panel', function ($user) {
            return $user->hasAnyRole(['admin', 'property_owner', 'receptionist']);
        });

        Gate::define('view-financial-data', function ($user) {
            return $user->hasAnyRole(['admin', 'property_owner']);
        });

        Gate::define('manage-visitors', function ($user) {
            return $user->hasAnyRole(['admin', 'receptionist']);
        });
    }
}
