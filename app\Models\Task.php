<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'priority',
        'status',
        'property_id',
        'unit_id',
        'assigned_to',
        'created_by',
        'due_date',
        'completed_at',
        'notes',
        'reminder_settings',
        'is_recurring',
        'recurrence_pattern',
        'next_occurrence',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'next_occurrence' => 'datetime',
        'reminder_settings' => 'array',
        'is_recurring' => 'boolean',
    ];

    // Relationships
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'related_task_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today())->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeUpcoming($query, $days = 7)
    {
        return $query->whereBetween('due_date', [now(), now()->addDays($days)])
            ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByProperty($query, $propertyId)
    {
        return $query->where('property_id', $propertyId);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    // Accessors
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getIsDueTodayAttribute(): bool
    {
        return $this->due_date && $this->due_date->isToday() && !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getIsUpcomingAttribute(): bool
    {
        return $this->due_date && $this->due_date->isFuture() && $this->due_date->diffInDays() <= 7 && !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getDaysUntilDueAttribute(): ?int
    {
        return $this->due_date ? $this->due_date->diffInDays(now(), false) : null;
    }

    // Methods
    public function markAsCompleted(): bool
    {
        return $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    public function createNextOccurrence(): ?Task
    {
        if (!$this->is_recurring || !$this->recurrence_pattern || !$this->next_occurrence) {
            return null;
        }

        $nextTask = $this->replicate();
        $nextTask->due_date = $this->next_occurrence;
        $nextTask->status = 'pending';
        $nextTask->completed_at = null;
        $nextTask->notes = null;

        // Calculate next occurrence based on pattern
        switch ($this->recurrence_pattern) {
            case 'daily':
                $nextTask->next_occurrence = $this->next_occurrence->addDay();
                break;
            case 'weekly':
                $nextTask->next_occurrence = $this->next_occurrence->addWeek();
                break;
            case 'monthly':
                $nextTask->next_occurrence = $this->next_occurrence->addMonth();
                break;
            case 'yearly':
                $nextTask->next_occurrence = $this->next_occurrence->addYear();
                break;
        }

        $nextTask->save();
        return $nextTask;
    }
}
