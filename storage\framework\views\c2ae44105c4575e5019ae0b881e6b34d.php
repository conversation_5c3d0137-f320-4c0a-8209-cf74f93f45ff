<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">Welcome to Admin Dashboard, <?php echo e(auth()->user()->name); ?>!</h4>
                        <p class="text-muted mb-0">
                            You have full administrative access to the Property Management System.
                        </p>
                    </div>
                    <div>
                        <form action="<?php echo e(route('logout')); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="ri-logout-box-line me-1"></i>Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards Row 1 -->
<div class="row gx-4 mb-4">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-primary rounded-circle me-3">
                        <div class="icon-box md bg-primary-lighten rounded-5">
                            <i class="ri-building-line fs-4 text-primary"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\Property::count()); ?></h2>
                        <p class="m-0">Total Properties</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-success rounded-circle me-3">
                        <div class="icon-box md bg-success-lighten rounded-5">
                            <i class="ri-home-line fs-4 text-success"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\Unit::count()); ?></h2>
                        <p class="m-0">Total Units</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-info rounded-circle me-3">
                        <div class="icon-box md bg-info-lighten rounded-5">
                            <i class="ri-group-line fs-4 text-info"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\Unit::where('status', 'occupied')->count()); ?></h2>
                        <p class="m-0">Total Residents</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-warning rounded-circle me-3">
                        <div class="icon-box md bg-warning-lighten rounded-5">
                            <i class="ri-customer-service-line fs-4 text-warning"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\Complaint::where('status', '!=', 'resolved')->count()); ?></h2>
                        <p class="m-0">Open Complaints</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards Row 2 -->
<div class="row gx-4 mb-4">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-purple rounded-circle me-3">
                        <div class="icon-box md bg-purple-lighten rounded-5">
                            <i class="ri-user-follow-line fs-4 text-purple"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\Visitor::whereDate('check_in_time', today())->count()); ?></h2>
                        <p class="m-0">Today's Visitors</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-danger rounded-circle me-3">
                        <div class="icon-box md bg-danger-lighten rounded-5">
                            <i class="ri-user-forbid-line fs-4 text-danger"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1"><?php echo e(\App\Models\BlacklistedVisitor::where('is_active', true)->count()); ?></h2>
                        <p class="m-0">Blacklisted Visitors</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-success rounded-circle me-3">
                        <div class="icon-box md bg-success-lighten rounded-5">
                            <i class="ri-money-dollar-circle-line fs-4 text-success"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">$<?php echo e(number_format(\App\Models\Bill::sum('total_amount'), 0)); ?></h2>
                        <p class="m-0">Total Billed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="p-2 border border-info rounded-circle me-3">
                        <div class="icon-box md bg-info-lighten rounded-5">
                            <i class="ri-money-dollar-box-line fs-4 text-info"></i>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="lh-1">$<?php echo e(number_format(\App\Models\Payment::where('status', 'completed')->sum('amount'), 0)); ?></h2>
                        <p class="m-0">Amount Collected</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-outline-primary w-100">
                            <i class="ri-user-add-line me-2"></i>Add New User
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-info w-100">
                            <i class="ri-group-line me-2"></i>Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="<?php echo e(route('admin.roles.create')); ?>" class="btn btn-outline-success w-100">
                            <i class="ri-shield-user-line me-2"></i>Add New Role
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="<?php echo e(route('admin.roles.index')); ?>" class="btn btn-outline-warning w-100">
                            <i class="ri-settings-line me-2"></i>Manage Roles
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row gx-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Users</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = \App\Models\User::latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($user->name); ?></td>
                                    <td><?php echo e($user->email); ?></td>
                                    <td>
                                        <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-primary"><?php echo e(ucfirst(str_replace('_', ' ', $role->name))); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </td>
                                    <td><?php echo e($user->created_at->diffForHumans()); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ri-dashboard-line text-primary me-2"></i>System Overview
                </h5>
            </div>
            <div class="card-body">
                <!-- Unit Status -->
                <h6 class="text-primary border-bottom pb-2 mb-3">Unit Status</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="ri-home-line me-1"></i>Available Units</span>
                    <span class="badge bg-success"><?php echo e(\App\Models\Unit::where('status', 'available')->count()); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="ri-home-fill me-1"></i>Occupied Units</span>
                    <span class="badge bg-info"><?php echo e(\App\Models\Unit::where('status', 'occupied')->count()); ?></span>
                </div>

                <!-- Visitor Status -->
                <h6 class="text-success border-bottom pb-2 mb-3">Visitor Management</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="ri-user-follow-line me-1"></i>Currently Checked In</span>
                    <span class="badge bg-primary"><?php echo e(\App\Models\Visitor::whereNotNull('check_in_time')->whereNull('check_out_time')->count()); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="ri-user-forbid-line me-1"></i>Blacklisted</span>
                    <span class="badge bg-danger"><?php echo e(\App\Models\BlacklistedVisitor::where('is_active', true)->count()); ?></span>
                </div>

                <!-- Financial Status -->
                <h6 class="text-warning border-bottom pb-2 mb-3">Financial Overview</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="ri-money-dollar-circle-line me-1"></i>Unpaid Bills</span>
                    <span class="badge bg-warning"><?php echo e(\App\Models\Bill::where('status', 'unpaid')->count()); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="ri-money-dollar-box-line me-1"></i>Amount Receivable</span>
                    <span class="badge bg-info">$<?php echo e(number_format(\App\Models\Bill::where('status', 'unpaid')->sum('total_amount'), 0)); ?></span>
                </div>

                <!-- Complaints Status -->
                <h6 class="text-danger border-bottom pb-2 mb-3">Complaints</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="ri-customer-service-line me-1"></i>Open Issues</span>
                    <span class="badge bg-warning"><?php echo e(\App\Models\Complaint::where('status', 'open')->count()); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="ri-check-line me-1"></i>Resolved Today</span>
                    <span class="badge bg-success"><?php echo e(\App\Models\Complaint::where('status', 'resolved')->whereDate('resolved_at', today())->count()); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>