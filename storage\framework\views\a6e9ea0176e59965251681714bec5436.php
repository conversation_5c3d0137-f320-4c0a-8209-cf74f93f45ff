<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-user-line text-primary me-2"></i>Visitor Management
                        </h4>
                        <p class="text-muted mb-0">
                            Track and manage all property visitors
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('visitors.create')); ?>" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In Visitor
                        </a>
                        <a href="<?php echo e(route('visitors.dashboard')); ?>" class="btn btn-outline-info">
                            <i class="ri-dashboard-line me-1"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['today_visitors']); ?></h3>
                                <p class="mb-0">Today's Visitors</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['checked_in']); ?></h3>
                                <p class="mb-0">Currently Checked In</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-user-check-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['this_week']); ?></h3>
                                <p class="mb-0">This Week</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-week-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['this_month']); ?></h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-month-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('visitors.index')); ?>" class="row g-3">
                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="<?php echo e(request('date', today()->format('Y-m-d'))); ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Visitors</option>
                            <option value="checked_in" <?php echo e(request('status') == 'checked_in' ? 'selected' : ''); ?>>Checked In</option>
                            <option value="checked_out" <?php echo e(request('status') == 'checked_out' ? 'selected' : ''); ?>>Checked Out</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="property_id" class="form-label">Property</label>
                        <select class="form-select" id="property_id" name="property_id">
                            <option value="">All Properties</option>
                            <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($property->id); ?>" <?php echo e(request('property_id') == $property->id ? 'selected' : ''); ?>>
                                    <?php echo e($property->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Name, phone, ID..." value="<?php echo e(request('search')); ?>">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Visitors Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Visitors List</h5>
            </div>
            <div class="card-body">
                <?php if($visitors->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Visitor</th>
                                    <th>Contact</th>
                                    <th>Visiting</th>
                                    <th>Purpose</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $visitors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $visitor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($visitor->name); ?></strong>
                                                <br><small class="text-muted">
                                                    <?php echo e(ucfirst(str_replace('_', ' ', $visitor->identification_type))); ?>: 
                                                    <?php echo e($visitor->identification_number); ?>

                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="ri-phone-line me-1"></i><?php echo e($visitor->phone); ?>

                                                <?php if($visitor->email): ?>
                                                    <br><i class="ri-mail-line me-1"></i><?php echo e($visitor->email); ?>

                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e($visitor->property->name ?? 'N/A'); ?></strong>
                                                <?php if($visitor->visitingUnit): ?>
                                                    <br><small class="text-muted">Unit <?php echo e($visitor->visitingUnit->unit_number); ?></small>
                                                <?php endif; ?>
                                                <?php if($visitor->visitingTenant): ?>
                                                    <br><small class="text-info"><?php echo e($visitor->visitingTenant->name); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($visitor->purpose_of_visit); ?></span>
                                            <?php if($visitor->vehicle_number): ?>
                                                <br><small class="text-muted">
                                                    <i class="ri-car-line me-1"></i><?php echo e($visitor->vehicle_number); ?>

                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($visitor->check_in_time): ?>
                                                <?php echo e($visitor->check_in_time->format('H:i')); ?>

                                                <br><small class="text-muted"><?php echo e($visitor->check_in_time->format('M d')); ?></small>
                                                <?php if($visitor->checkedInBy): ?>
                                                    <br><small class="text-info">by <?php echo e($visitor->checkedInBy->name); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not checked in</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($visitor->check_out_time): ?>
                                                <?php echo e($visitor->check_out_time->format('H:i')); ?>

                                                <br><small class="text-muted"><?php echo e($visitor->check_out_time->format('M d')); ?></small>
                                                <?php if($visitor->checkedOutBy): ?>
                                                    <br><small class="text-info">by <?php echo e($visitor->checkedOutBy->name); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Still inside</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($visitor->visit_duration): ?>
                                                <span class="badge bg-info"><?php echo e($visitor->visit_duration); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($visitor->is_checked_in): ?>
                                                <span class="badge bg-success">Checked In</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Checked Out</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('visitors.show', $visitor)); ?>" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <?php if($visitor->is_checked_in): ?>
                                                    <button type="button" class="btn btn-outline-danger check-out-visitor" 
                                                            data-visitor-id="<?php echo e($visitor->id); ?>" 
                                                            data-visitor-name="<?php echo e($visitor->name); ?>" title="Check Out">
                                                        <i class="ri-logout-circle-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <a href="<?php echo e(route('visitors.edit', $visitor)); ?>" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($visitors->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-user-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Visitors Found</h5>
                        <p class="text-muted mb-4">
                            <?php if(request('search') || request('status') || request('property_id')): ?>
                                No visitors match your search criteria.
                            <?php else: ?>
                                No visitors have been registered for the selected date.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo e(route('visitors.create')); ?>" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In First Visitor
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check out visitor
    document.querySelectorAll('.check-out-visitor').forEach(button => {
        button.addEventListener('click', function() {
            const visitorId = this.dataset.visitorId;
            const visitorName = this.dataset.visitorName;

            Swal.fire({
                title: 'Check Out Visitor',
                html: `Are you sure you want to check out:<br><br><strong>${visitorName}</strong>?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-logout-circle-line me-1"></i>Check Out',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/visitors/${visitorId}/check-out`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Auto-refresh page every 30 seconds for real-time updates
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            window.location.reload();
        }
    }, 30000);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/visitors/index.blade.php ENDPATH**/ ?>