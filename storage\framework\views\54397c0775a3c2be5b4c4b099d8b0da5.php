<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-alarm-warning-line text-warning me-2"></i>Expiring Documents
                        </h4>
                        <p class="text-muted mb-0">
                            Documents that are expiring soon or have already expired
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('documents.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Upload Document
                        </a>
                        <a href="<?php echo e(route('documents.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>All Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($expiredCount ?? 0); ?></h3>
                                <p class="mb-0">Expired Documents</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-error-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($expiringSoonCount ?? 0); ?></h3>
                                <p class="mb-0">Expiring Soon</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($totalCount ?? 0); ?></h3>
                                <p class="mb-0">Total Documents</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-file-list-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expired Documents -->
        <?php if(isset($expiredDocuments) && $expiredDocuments->count() > 0): ?>
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="ri-error-warning-line me-2"></i>Expired Documents (<?php echo e($expiredDocuments->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Property</th>
                                <th>Expiry Date</th>
                                <th>Days Overdue</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $expiredDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <?php if(in_array($document->file_type, ['jpg', 'jpeg', 'png'])): ?>
                                                    <i class="ri-image-line fs-4 text-info"></i>
                                                <?php elseif($document->file_type === 'pdf'): ?>
                                                    <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                                <?php else: ?>
                                                    <i class="ri-file-line fs-4 text-muted"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><?php echo e($document->title); ?></h6>
                                                <small class="text-muted"><?php echo e($document->file_name); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $document->type))); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($document->property): ?>
                                            <span class="text-primary"><?php echo e($document->property->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No property</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-danger fw-bold">
                                            <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo e(abs($document->expiry_date->diffInDays(now()))); ?> days
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('documents.show', $document)); ?>" class="btn btn-outline-primary" title="View">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="<?php echo e(route('documents.edit', $document)); ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <a href="<?php echo e(route('documents.download', $document)); ?>" class="btn btn-outline-success" title="Download">
                                                <i class="ri-download-line"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Expiring Soon Documents -->
        <?php if(isset($expiringSoonDocuments) && $expiringSoonDocuments->count() > 0): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="ri-alarm-warning-line me-2"></i>Expiring Soon (<?php echo e($expiringSoonDocuments->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Property</th>
                                <th>Expiry Date</th>
                                <th>Days Remaining</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $expiringSoonDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <?php if(in_array($document->file_type, ['jpg', 'jpeg', 'png'])): ?>
                                                    <i class="ri-image-line fs-4 text-info"></i>
                                                <?php elseif($document->file_type === 'pdf'): ?>
                                                    <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                                <?php else: ?>
                                                    <i class="ri-file-line fs-4 text-muted"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><?php echo e($document->title); ?></h6>
                                                <small class="text-muted"><?php echo e($document->file_name); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $document->type))); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($document->property): ?>
                                            <span class="text-primary"><?php echo e($document->property->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No property</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-warning fw-bold">
                                            <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <?php echo e($document->expiry_date->diffInDays(now())); ?> days
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('documents.show', $document)); ?>" class="btn btn-outline-primary" title="View">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="<?php echo e(route('documents.edit', $document)); ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <a href="<?php echo e(route('documents.download', $document)); ?>" class="btn btn-outline-success" title="Download">
                                                <i class="ri-download-line"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- No Expiring Documents -->
        <?php if((!isset($expiredDocuments) || $expiredDocuments->count() === 0) && (!isset($expiringSoonDocuments) || $expiringSoonDocuments->count() === 0)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="ri-shield-check-line fs-1 text-success mb-3"></i>
                <h4 class="text-success mb-2">All Documents Are Up to Date!</h4>
                <p class="text-muted mb-4">
                    Great news! You don't have any documents that are expired or expiring soon.
                </p>
                <div class="d-flex justify-content-center gap-2">
                    <a href="<?php echo e(route('documents.index')); ?>" class="btn btn-outline-primary">
                        <i class="ri-file-list-line me-1"></i>View All Documents
                    </a>
                    <a href="<?php echo e(route('documents.create')); ?>" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Upload New Document
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" onclick="sendExpiryAlerts()">
                                <i class="ri-mail-send-line me-2"></i>Send Expiry Alerts
                            </button>
                            <a href="<?php echo e(route('documents.create')); ?>" class="btn btn-primary">
                                <i class="ri-upload-line me-2"></i>Upload New Document
                            </a>
                            <a href="<?php echo e(route('documents.index')); ?>" class="btn btn-outline-secondary">
                                <i class="ri-file-list-line me-2"></i>View All Documents
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Document Types</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-2">
                                <small class="text-muted">Insurance</small>
                                <div class="fw-bold"><?php echo e($typeStats['insurance'] ?? 0); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Fire Certificate</small>
                                <div class="fw-bold"><?php echo e($typeStats['fire_certificate'] ?? 0); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Contracts</small>
                                <div class="fw-bold"><?php echo e($typeStats['contract'] ?? 0); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Permits</small>
                                <div class="fw-bold"><?php echo e($typeStats['permit'] ?? 0); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function sendExpiryAlerts() {
    Swal.fire({
        title: 'Send Expiry Alerts',
        text: 'This will send email notifications for all expiring documents. Continue?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="ri-mail-send-line me-1"></i>Send Alerts',
        cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Sending Alerts...',
                text: 'Please wait while we send the expiry alerts.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send request
            fetch('<?php echo e(route("documents.send-expiry-alerts")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: data.message || 'Expiry alerts sent successfully.',
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    throw new Error(data.message || 'Failed to send alerts');
                }
            })
            .catch(error => {
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to send expiry alerts.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/documents/expiring.blade.php ENDPATH**/ ?>