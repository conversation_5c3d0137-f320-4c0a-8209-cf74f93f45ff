<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-feedback-line text-primary me-2"></i>Complaints Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage and track all tenant complaints and issues
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('complaints.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>New Complaint
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index')); ?>">All Complaints</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['status' => 'open'])); ?>">Open</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['status' => 'in_progress'])); ?>">In Progress</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['status' => 'resolved'])); ?>">Resolved</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['status' => 'closed'])); ?>">Closed</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['priority' => 'high'])); ?>">High Priority</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['priority' => 'medium'])); ?>">Medium Priority</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('complaints.index', ['priority' => 'low'])); ?>">Low Priority</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['open_count'] ?? 0); ?></h3>
                                <p class="mb-0">Open Complaints</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-error-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['in_progress_count'] ?? 0); ?></h3>
                                <p class="mb-0">In Progress</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['resolved_count'] ?? 0); ?></h3>
                                <p class="mb-0">Resolved</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-check-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($stats['high_priority_count'] ?? 0); ?></h3>
                                <p class="mb-0">High Priority</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Complaints Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Complaints List</h5>
                    <div class="d-flex gap-2">
                        <form method="GET" action="<?php echo e(route('complaints.index')); ?>" class="d-flex gap-2">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search complaints..." value="<?php echo e(request('search')); ?>">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if(isset($complaints) && $complaints->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tenant</th>
                                    <th>Property/Unit</th>
                                    <th>Subject</th>
                                    <th>Category</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $complaints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $complaint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong>#<?php echo e($complaint->id); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($complaint->tenant): ?>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($complaint->tenant->name); ?></h6>
                                                    <small class="text-muted"><?php echo e($complaint->tenant->email); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">No tenant</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($complaint->unit): ?>
                                                <div>
                                                    <strong><?php echo e($complaint->unit->property->name ?? 'Unknown Property'); ?></strong>
                                                    <br><small class="text-muted">Unit <?php echo e($complaint->unit->unit_number); ?></small>
                                                </div>
                                            <?php elseif($complaint->property): ?>
                                                <strong><?php echo e($complaint->property->name); ?></strong>
                                            <?php else: ?>
                                                <span class="text-muted">No property</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e(Str::limit($complaint->subject ?? 'No Subject', 30)); ?></strong>
                                                <?php if($complaint->description): ?>
                                                    <br><small class="text-muted"><?php echo e(Str::limit($complaint->description, 50)); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $complaint->category ?? 'general'))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                                $priorityColors = [
                                                    'low' => 'success',
                                                    'medium' => 'warning',
                                                    'high' => 'danger',
                                                    'urgent' => 'dark'
                                                ];
                                                $priorityColor = $priorityColors[$complaint->priority ?? 'medium'] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo e($priorityColor); ?>">
                                                <?php echo e(ucfirst($complaint->priority ?? 'medium')); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'open' => 'warning',
                                                    'in_progress' => 'info',
                                                    'resolved' => 'success',
                                                    'closed' => 'secondary',
                                                    'cancelled' => 'dark'
                                                ];
                                                $statusColor = $statusColors[$complaint->status ?? 'open'] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColor); ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $complaint->status ?? 'open'))); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($complaint->created_at): ?>
                                                <?php echo e($complaint->created_at->format('M d, Y')); ?>

                                                <br><small class="text-muted"><?php echo e($complaint->created_at->format('H:i')); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">No date</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('complaints.show', $complaint)); ?>" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="<?php echo e(route('complaints.edit', $complaint)); ?>" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                <?php if($complaint->status !== 'resolved' && $complaint->status !== 'closed'): ?>
                                                    <button type="button" class="btn btn-outline-success resolve-complaint" 
                                                            data-complaint-id="<?php echo e($complaint->id); ?>" title="Mark as Resolved">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if(method_exists($complaints, 'links')): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($complaints->links()); ?>

                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ri-feedback-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Complaints Found</h5>
                        <p class="text-muted mb-4">
                            <?php if(request('search')): ?>
                                No complaints match your search criteria.
                            <?php else: ?>
                                No complaints have been submitted yet.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo e(route('complaints.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First Complaint
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Resolve complaint
    document.querySelectorAll('.resolve-complaint').forEach(button => {
        button.addEventListener('click', function() {
            const complaintId = this.dataset.complaintId;

            Swal.fire({
                title: 'Mark as Resolved',
                text: 'Are you sure you want to mark this complaint as resolved?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Mark as Resolved',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/complaints/${complaintId}/resolve`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/complaints/index.blade.php ENDPATH**/ ?>