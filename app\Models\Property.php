<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Property extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'property_type',
        'total_area',
        'total_units',
        'owner_id',
        'manager_id',
        'monthly_rent',
        'purchase_date',
        'purchase_price',
        'status',
        'amenities',
    ];

    protected $casts = [
        'amenities' => 'array',
        'purchase_date' => 'date',
        'purchase_price' => 'decimal:2',
        'monthly_rent' => 'decimal:2',
        'total_area' => 'decimal:2',
    ];

    // Relationships
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function units(): HasMany
    {
        return $this->hasMany(Unit::class);
    }

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('property_type', $type);
    }
}
