<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-home-line text-primary me-2"></i>Unit Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage units, track occupancy, and assign tenants
                        </p>
                    </div>
                    <div>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Unit::class)): ?>
                            <a href="<?php echo e(route('units.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Add New Unit
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('units.index')); ?>" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" placeholder="Unit number, tenant name...">
                    </div>
                    <div class="col-md-2">
                        <label for="property_id" class="form-label">Property</label>
                        <select class="form-select" id="property_id" name="property_id">
                            <option value="">All Properties</option>
                            <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($property->id); ?>" <?php echo e(request('property_id') == $property->id ? 'selected' : ''); ?>>
                                    <?php echo e($property->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="available" <?php echo e(request('status') == 'available' ? 'selected' : ''); ?>>Available</option>
                            <option value="occupied" <?php echo e(request('status') == 'occupied' ? 'selected' : ''); ?>>Occupied</option>
                            <option value="maintenance" <?php echo e(request('status') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                            <option value="reserved" <?php echo e(request('status') == 'reserved' ? 'selected' : ''); ?>>Reserved</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="unit_type" class="form-label">Type</label>
                        <select class="form-select" id="unit_type" name="unit_type">
                            <option value="">All Types</option>
                            <?php $__currentLoopData = $unitTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type); ?>" <?php echo e(request('unit_type') == $type ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst($type)); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('units.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Units Grid -->
        <?php if($units->count() > 0): ?>
            <div class="row">
                <?php $__currentLoopData = $units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 unit-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="ri-home-line text-primary me-1"></i>
                                    Unit <?php echo e($unit->unit_number); ?>

                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="ri-more-line"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('units.show', $unit)); ?>">
                                                <i class="ri-eye-line me-1"></i>View Details
                                            </a>
                                        </li>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $unit)): ?>
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('units.edit', $unit)); ?>">
                                                    <i class="ri-edit-line me-1"></i>Edit Unit
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manageOccupancy', $unit)): ?>
                                            <?php if($unit->tenant_id): ?>
                                                <li>
                                                    <button class="dropdown-item text-warning remove-tenant" 
                                                            data-unit-id="<?php echo e($unit->id); ?>"
                                                            data-unit-number="<?php echo e($unit->unit_number); ?>">
                                                        <i class="ri-user-unfollow-line me-1"></i>Remove Tenant
                                                    </button>
                                                </li>
                                            <?php else: ?>
                                                <li>
                                                    <a class="dropdown-item text-success" href="<?php echo e(route('units.show', $unit)); ?>#assign-tenant">
                                                        <i class="ri-user-add-line me-1"></i>Assign Tenant
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $unit)): ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-unit" 
                                                        data-unit-id="<?php echo e($unit->id); ?>"
                                                        data-unit-number="<?php echo e($unit->unit_number); ?>">
                                                    <i class="ri-delete-bin-line me-1"></i>Delete
                                                </button>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Unit Info -->
                                <div class="mb-3">
                                    <p class="text-muted mb-1">
                                        <i class="ri-building-line me-1"></i>
                                        <?php echo e($unit->property->name); ?>

                                    </p>
                                    <p class="text-muted mb-1">
                                        <i class="ri-home-gear-line me-1"></i>
                                        <?php echo e(ucfirst($unit->unit_type)); ?> • <?php echo e($unit->bedrooms); ?>BR/<?php echo e($unit->bathrooms); ?>BA
                                    </p>
                                    <?php if($unit->area): ?>
                                        <p class="text-muted mb-1">
                                            <i class="ri-ruler-line me-1"></i>
                                            <?php echo e(number_format($unit->area, 0)); ?> sq ft
                                        </p>
                                    <?php endif; ?>
                                    <span class="badge bg-<?php echo e($unit->status == 'available' ? 'success' : ($unit->status == 'occupied' ? 'primary' : ($unit->status == 'maintenance' ? 'warning' : 'secondary'))); ?>">
                                        <?php echo e(ucfirst($unit->status)); ?>

                                    </span>
                                </div>

                                <!-- Tenant Info -->
                                <?php if($unit->tenant): ?>
                                    <div class="mb-3 p-2 bg-light rounded">
                                        <h6 class="mb-1">
                                            <i class="ri-user-line text-primary me-1"></i>Current Tenant
                                        </h6>
                                        <p class="mb-1"><?php echo e($unit->tenant->name); ?></p>
                                        <?php if($unit->lease_end): ?>
                                            <small class="text-muted">
                                                Lease expires: <?php echo e($unit->lease_end->format('M d, Y')); ?>

                                                <?php if($unit->lease_end->isPast()): ?>
                                                    <span class="badge bg-danger ms-1">Expired</span>
                                                <?php elseif($unit->lease_end->diffInDays() <= 30): ?>
                                                    <span class="badge bg-warning ms-1">Expiring Soon</span>
                                                <?php endif; ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Financial Info -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="text-muted">Monthly Rent:</span>
                                    <span class="fw-bold text-success">$<?php echo e(number_format($unit->rent_amount, 0)); ?></span>
                                </div>

                                <?php if($unit->security_deposit): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="text-muted">Security Deposit:</span>
                                        <span class="fw-bold">$<?php echo e(number_format($unit->security_deposit, 0)); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex gap-2">
                                    <a href="<?php echo e(route('units.show', $unit)); ?>" class="btn btn-primary btn-sm flex-fill">
                                        <i class="ri-eye-line me-1"></i>View Details
                                    </a>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $unit)): ?>
                                        <a href="<?php echo e(route('units.edit', $unit)); ?>" class="btn btn-outline-warning btn-sm">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                <?php echo e($units->links()); ?>

            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-home-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Units Found</h5>
                    <p class="text-muted mb-4">
                        <?php if(request()->hasAny(['search', 'property_id', 'status', 'unit_type'])): ?>
                            No units match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            You haven't added any units yet. Start by adding your first unit.
                        <?php endif; ?>
                    </p>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Unit::class)): ?>
                        <a href="<?php echo e(route('units.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Your First Unit
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete unit functionality
    document.querySelectorAll('.delete-unit').forEach(button => {
        button.addEventListener('click', function() {
            const unitId = this.dataset.unitId;
            const unitNumber = this.dataset.unitNumber;

            Swal.fire({
                title: 'Delete Unit',
                html: `Are you sure you want to delete:<br><br><strong>Unit ${unitNumber}</strong>?<br><br><small class="text-danger">This action cannot be undone and will delete all associated data.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/units/${unitId}`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Remove tenant functionality
    document.querySelectorAll('.remove-tenant').forEach(button => {
        button.addEventListener('click', function() {
            const unitId = this.dataset.unitId;
            const unitNumber = this.dataset.unitNumber;

            Swal.fire({
                title: 'Remove Tenant',
                html: `Are you sure you want to remove the tenant from:<br><br><strong>Unit ${unitNumber}</strong>?<br><br><small class="text-warning">This will set the unit status to available.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-user-unfollow-line me-1"></i>Yes, Remove',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/units/${unitId}/remove-tenant`;
                    form.innerHTML = `
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/units/index.blade.php ENDPATH**/ ?>